import { Router, type Request, type Response } from 'express';
import { storage } from '../storage';
import fs from 'fs/promises';
import path from 'path';

const forceInitRouter = Router();

// Force initialize checkout pages from default configuration
forceInitRouter.post('/checkout-pages', async (req: Request, res: Response) => {
  try {
    console.log('🚀 Force initializing checkout pages from default configuration...');
    
    // Load the default configuration directly
    const configPath = path.join(process.cwd(), 'server', 'default-configs', 'default-configuration.json');
    const configData = await fs.readFile(configPath, 'utf-8');
    const config = JSON.parse(configData);
    
    console.log(`📦 Loading configuration v${config.version} from ${config.exportDate}`);
    
    const checkoutPages = config.data.customCheckoutPages;
    
    if (!checkoutPages || checkoutPages.length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'No checkout pages found in configuration' 
      });
    }
    
    console.log(`📄 Found ${checkoutPages.length} checkout pages to initialize`);
    
    // Clear existing checkout pages in memory
    const existingPages = await storage.getCustomCheckoutPages();
    console.log(`🗑️ Clearing ${existingPages.length} existing checkout pages from memory`);
    
    for (const page of existingPages) {
      await storage.deleteCustomCheckoutPage(page.id);
    }
    
    // Add all checkout pages from configuration
    let successCount = 0;
    const createdPages = [];
    
    for (const page of checkoutPages) {
      try {
        const { id, ...pageData } = page;
        const createdPage = await storage.createCustomCheckoutPage({
          ...pageData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        
        console.log(`✅ Created checkout page: ${createdPage.title} (ID: ${createdPage.id})`);
        createdPages.push(createdPage);
        successCount++;
      } catch (error) {
        console.error(`❌ Error creating checkout page ${page.title}:`, error);
      }
    }
    
    console.log(`🎉 Successfully initialized ${successCount}/${checkoutPages.length} checkout pages!`);
    
    // Verify the pages were created
    const finalPages = await storage.getCustomCheckoutPages();
    console.log(`📊 Verification: ${finalPages.length} checkout pages now in memory`);
    
    res.json({
      success: true,
      message: `Successfully initialized ${successCount}/${checkoutPages.length} checkout pages`,
      data: {
        totalPages: finalPages.length,
        createdPages: createdPages.map(page => ({
          id: page.id,
          title: page.title,
          slug: page.slug,
          productName: page.productName,
          price: page.price,
          isTrialCheckout: page.isTrialCheckout
        }))
      }
    });
    
  } catch (error) {
    console.error('❌ Error during force initialization:', error);
    res.status(500).json({
      success: false,
      message: 'Error during force initialization',
      error: error.message
    });
  }
});

// Get current checkout pages count
forceInitRouter.get('/checkout-pages/status', async (req: Request, res: Response) => {
  try {
    const pages = await storage.getCustomCheckoutPages();
    
    res.json({
      success: true,
      data: {
        totalPages: pages.length,
        pages: pages.map(page => ({
          id: page.id,
          title: page.title,
          slug: page.slug,
          productName: page.productName,
          price: page.price,
          isTrialCheckout: page.isTrialCheckout
        }))
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting checkout pages status:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting checkout pages status',
      error: error.message
    });
  }
});

export { forceInitRouter };
