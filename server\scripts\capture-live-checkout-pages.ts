#!/usr/bin/env tsx

// This script needs to be run while the app is running to capture live checkout pages
import fetch from 'node-fetch';
import fs from 'fs/promises';
import path from 'path';

async function captureLiveCheckoutPages() {
  console.log('🔍 Capturing live checkout pages from running app...');
  
  try {
    // Try different API endpoints to get checkout pages
    const endpoints = [
      'http://localhost:3001/api/admin/custom-checkout',
      'http://localhost:3001/api/custom-checkout',
      'http://localhost:3001/api/custom-checkout/list'
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 Trying endpoint: ${endpoint}`);
        
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Successfully fetched from ${endpoint}`);
          
          if (Array.isArray(data) && data.length > 0) {
            console.log(`🎉 FOUND ${data.length} CHECKOUT PAGES!`);
            
            data.forEach((page: any, index: number) => {
              console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
              console.log(`      Product: ${page.productName}`);
              console.log(`      Price: $${page.price}`);
              console.log(`      Payment Method: ${page.paymentMethod}`);
              console.log(`      Views: ${page.views || 0}, Conversions: ${page.conversions || 0}`);
              console.log('');
            });
            
            // Save the checkout pages for export
            const exportData = {
              version: '2.0',
              exportDate: new Date().toISOString(),
              source: 'live-api-capture',
              customCheckoutPages: data
            };
            
            const configDir = path.join(process.cwd(), 'server', 'default-configs');
            await fs.mkdir(configDir, { recursive: true });
            
            const exportPath = path.join(configDir, 'live-checkout-pages-export.json');
            await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2));
            
            console.log(`✅ Checkout pages exported to: ${exportPath}`);
            
            // Now update the main default configuration with these checkout pages
            await updateDefaultConfigWithCheckoutPages(data);
            
            return data;
          } else {
            console.log(`📄 No checkout pages found at ${endpoint}`);
          }
        } else {
          console.log(`❌ ${endpoint} failed: ${response.status} ${response.statusText}`);
        }
        
      } catch (error) {
        console.log(`❌ Error with ${endpoint}: ${error.message}`);
      }
    }
    
    // If API doesn't work, try to make a direct call to the storage
    console.log('\n🔍 Trying direct storage access...');
    try {
      // Import the storage directly
      const { storage } = await import('../storage');
      const pages = await storage.getCustomCheckoutPages();
      
      if (pages.length > 0) {
        console.log(`🎉 FOUND ${pages.length} CHECKOUT PAGES in direct storage!`);
        
        pages.forEach((page: any, index: number) => {
          console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
          console.log(`      Product: ${page.productName}`);
          console.log(`      Price: $${page.price}`);
          console.log(`      Payment Method: ${page.paymentMethod}`);
          console.log(`      Views: ${page.views || 0}, Conversions: ${page.conversions || 0}`);
          console.log('');
        });
        
        // Save the checkout pages for export
        const exportData = {
          version: '2.0',
          exportDate: new Date().toISOString(),
          source: 'direct-storage-capture',
          customCheckoutPages: pages
        };
        
        const configDir = path.join(process.cwd(), 'server', 'default-configs');
        await fs.mkdir(configDir, { recursive: true });
        
        const exportPath = path.join(configDir, 'direct-checkout-pages-export.json');
        await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2));
        
        console.log(`✅ Checkout pages exported to: ${exportPath}`);
        
        // Now update the main default configuration with these checkout pages
        await updateDefaultConfigWithCheckoutPages(pages);
        
        return pages;
      } else {
        console.log('📄 No checkout pages found in direct storage access');
      }
    } catch (error) {
      console.log(`❌ Error with direct storage access: ${error.message}`);
    }
    
    console.log('\n❌ No checkout pages found through any method');
    
  } catch (error) {
    console.error('❌ Error capturing checkout pages:', error);
  }
}

async function updateDefaultConfigWithCheckoutPages(checkoutPages: any[]) {
  try {
    console.log('\n🔄 Updating default configuration with captured checkout pages...');
    
    const configPath = path.join(process.cwd(), 'server', 'default-configs', 'default-configuration.json');
    
    // Read existing config
    let config;
    try {
      const configData = await fs.readFile(configPath, 'utf-8');
      config = JSON.parse(configData);
    } catch (error) {
      console.log('❌ Could not read existing config, creating new one');
      config = {
        version: '2.0',
        exportDate: new Date().toISOString(),
        metadata: {
          description: 'Complete default configuration export including all user customizations',
          source: 'live-capture-update'
        },
        data: {
          customCheckoutPages: [],
          allowedEmails: [],
          smtpProviders: [],
          emailTemplates: [],
          paypalButtons: [],
          generalSettings: {},
          paymentConfig: {},
          uploadedImages: []
        }
      };
    }
    
    // Update with captured checkout pages
    config.data.customCheckoutPages = checkoutPages;
    config.exportDate = new Date().toISOString();
    config.metadata.source = 'live-capture-update';
    
    // Create backup of old config
    const backupPath = path.join(
      path.dirname(configPath), 
      `default-config-backup-before-checkout-update-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
    );
    
    try {
      await fs.copyFile(configPath, backupPath);
      console.log(`✅ Backup created: ${backupPath}`);
    } catch (error) {
      console.log('⚠️ Could not create backup, proceeding anyway');
    }
    
    // Save updated config
    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
    
    console.log(`✅ Updated default configuration with ${checkoutPages.length} checkout pages!`);
    console.log(`📁 Updated file: ${configPath}`);
    
  } catch (error) {
    console.error('❌ Error updating default configuration:', error);
  }
}

// Run the capture
captureLiveCheckoutPages().catch(console.error);
