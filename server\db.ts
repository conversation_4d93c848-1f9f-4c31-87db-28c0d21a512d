import { drizzle } from 'drizzle-orm/better-sqlite3';
import { drizzle as drizzleMySQL } from 'drizzle-orm/mysql2';
import Database from 'better-sqlite3';
import mysql from 'mysql2/promise';
import * as schema from '../shared/schema';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment
const DATABASE_URL = process.env.DATABASE_URL || 'sqlite:./data.db';

// Determine database type
const isSQLite = DATABASE_URL.startsWith('sqlite:');
const isMySQL = DATABASE_URL.startsWith('mysql:');

let db: any;

if (isSQLite) {
  // Initialize SQLite database
  const sqlite = new Database('./server/data.db');
  db = drizzle(sqlite, { schema });
  console.log('🗄️ Connected to SQLite database');
} else if (isMySQL) {
  // Initialize MySQL database
  const connection = mysql.createPool(DATABASE_URL);
  db = drizzleMySQL(connection, { schema, mode: 'default' });
  console.log('🗄️ Connected to MySQL database');
} else {
  throw new Error(`Unsupported database type in DATABASE_URL: ${DATABASE_URL}`);
}

export { db };
export default db;
