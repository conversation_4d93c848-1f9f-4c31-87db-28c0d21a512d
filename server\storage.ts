import {
  users,
  products,
  invoices,
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  smtpProviders,
  embedCodes,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice,
  type SmtpProvider,
  type InsertSmtpProvider,
  type EmbedCode,
  type InsertEmbedCode
} from "@shared/schema";
import { createHash, randomBytes } from 'crypto';
import { db } from './db';
import { eq, sql } from 'drizzle-orm';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

// Recovery code interface
interface RecoveryCode {
  code: string;
  used: boolean;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyUserCredentials(username: string, password: string): Promise<boolean>;
  verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean>;
  saveResetToken(userId: number, token: string, expiry: Date): Promise<void>;
  validateResetToken(token: string): Promise<boolean>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  updateUserPassword(userId: number, password: string): Promise<void>;
  clearResetToken(userId: number): Promise<void>;
  updateUsername(userId: number, username: string): Promise<void>;
  updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void>;
  enableTwoFactor(userId: number, secret: string): Promise<void>;
  disableTwoFactor(userId: number): Promise<void>;
  verifyTwoFactorToken(userId: number, token: string): Promise<boolean>;

  // Recovery code methods
  generateRecoveryCodes(userId: number): Promise<string[]>;
  verifyRecoveryCode(userId: number, code: string): Promise<boolean>;

  // Device tracking methods
  addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device>;
  getDevices(userId: number): Promise<Device[]>;
  updateDeviceLastLogin(userId: number, deviceId: string): Promise<void>;
  removeDevice(userId: number, deviceId: string): Promise<boolean>;

  // Product methods
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  createProduct(product: InsertProduct): Promise<Product>;

  // Invoice methods
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined>;

  // Configuration methods
  getGeneralSettings(): Promise<any>;
  getEmailConfig(): Promise<any>;
  getPaymentConfig(): Promise<any>;

  // Custom Checkout Page methods
  createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage>;
  getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPages(): Promise<CustomCheckoutPage[]>;
  updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined>;
  incrementCustomCheckoutPageViews(id: number): Promise<void>;
  incrementCustomCheckoutPageConversions(id: number): Promise<void>;
  deleteCustomCheckoutPage(id: number): Promise<boolean>;

  // Allowed Email methods
  getAllowedEmails(): Promise<AllowedEmail[]>;
  getAllowedEmail(id: number): Promise<AllowedEmail | undefined>;
  getEmailByAddress(email: string): Promise<AllowedEmail | undefined>;
  isEmailAllowed(email: string): Promise<boolean>;
  createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail>;
  updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined>;
  updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail>;
  deleteAllowedEmail(id: number): Promise<boolean>;
  bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }>;

  // Email Template methods
  getEmailTemplates(): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;
  deleteEmailTemplate(id: number): Promise<boolean>;

  // PayPal Button methods
  getPaypalButtons(): Promise<PaypalButton[]>;
  getPaypalButton(id: number): Promise<PaypalButton | undefined>;
  createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton>;
  updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined>;
  deletePaypalButton(id: number): Promise<boolean>;

  // Custom Invoice methods
  getCustomInvoices(): Promise<CustomInvoice[]>;
  getCustomInvoice(id: number): Promise<CustomInvoice | undefined>;
  getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined>;
  createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice>;
  updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined>;
  incrementCustomInvoiceViewCount(id: number): Promise<void>;
  markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined>;
  deleteCustomInvoice(id: number): Promise<boolean>;

  // Contact Inquiry methods
  createContactInquiry?(inquiry: any): Promise<any>;
  getContactInquiries?(): Promise<any[]>;
  updateContactInquiry?(id: number, update: any): Promise<any>;

  // Embed Code methods
  getEmbedCodes(): Promise<EmbedCode[]>;
  getEmbedCode(id: string): Promise<EmbedCode | undefined>;
  createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode>;
  updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined>;
  deleteEmbedCode(id: string): Promise<boolean>;

  // SMTP Provider methods
  getSmtpProviders(): Promise<SmtpProvider[]>;
  getSmtpProvider(id: string): Promise<SmtpProvider | undefined>;
  createSmtpProvider(provider: InsertSmtpProvider): Promise<SmtpProvider>;
  updateSmtpProvider(id: string, update: Partial<InsertSmtpProvider>): Promise<SmtpProvider | undefined>;
  deleteSmtpProvider(id: string): Promise<boolean>;
  getDefaultSmtpProvider(): Promise<SmtpProvider | undefined>;
  setDefaultSmtpProvider(id: string): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  private db: typeof db;

  constructor() {
    this.db = db;
    console.log('🗄️ DatabaseStorage initialized - all data will be stored in database');
  }

  // Helper method to parse JSON fields safely
  private parseJsonField<T>(field: string | null, defaultValue: T): T {
    if (!field) return defaultValue;
    try {
      return JSON.parse(field);
    } catch {
      return defaultValue;
    }
  }

  // Helper method to stringify JSON fields
  private stringifyJsonField<T>(value: T): string {
    return JSON.stringify(value);
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
      if (!result[0]) return undefined;
      
      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.username, username)).limit(1);
      if (!result[0]) return undefined;
      
      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
      if (!result[0]) return undefined;
      
      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user by email:', error);
      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      // Hash the password if it's not already hashed
      let password = insertUser.password;
      if (!password.match(/^[0-9a-f]{64}$/i)) {
        password = createHash('sha256').update(password).digest('hex');
      }

      const userData = {
        ...insertUser,
        password,
        rememberMe: insertUser.rememberMe || false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: undefined,
        twoFactorEnabled: false,
        recoveryCodes: this.stringifyJsonField([]),
        devices: this.stringifyJsonField([])
      };

      const result = await this.db.insert(users).values(userData).returning();
      const user = result[0];
      
      return {
        ...user,
        recoveryCodes: [],
        devices: []
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          resetToken: token,
          resetTokenExpiry: expiry.toISOString()
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error saving reset token:', error);
    }
  }

  async validateResetToken(token: string): Promise<boolean> {
    const user = await this.getUserByResetToken(token);
    if (!user) return false;

    // Check if token is expired
    if (new Date() > new Date(user.resetTokenExpiry!)) {
      return false;
    }

    return true;
  }

  async getUserByResetToken(token: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.resetToken, token)).limit(1);
      if (!result[0]) return undefined;

      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user by reset token:', error);
      return undefined;
    }
  }

  async updateUserPassword(userId: number, password: string): Promise<void> {
    try {
      await this.db.update(users)
        .set({ password })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating user password:', error);
    }
  }

  async clearResetToken(userId: number): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          resetToken: null,
          resetTokenExpiry: null
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error clearing reset token:', error);
    }
  }

  async updateUsername(userId: number, username: string): Promise<void> {
    try {
      await this.db.update(users)
        .set({ username })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating username:', error);
    }
  }

  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {
    try {
      await this.db.update(users)
        .set({ rememberMe })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating auto login settings:', error);
    }
  }

  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          twoFactorSecret: secret,
          twoFactorEnabled: true
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error enabling two factor:', error);
    }
  }

  async disableTwoFactor(userId: number): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          twoFactorSecret: null,
          twoFactorEnabled: false
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error disabling two factor:', error);
    }
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) return false;

    // The actual verification is done in the auth routes using otplib
    // This method is just a placeholder for the interface
    return true;
  }

  // Recovery code methods
  async generateRecoveryCodes(userId: number): Promise<string[]> {
    const user = await this.getUser(userId);
    if (!user) return [];

    // Generate 10 recovery codes
    const codes: string[] = [];
    const recoveryCodes: RecoveryCode[] = [];

    for (let i = 0; i < 10; i++) {
      // Generate a random 8-character code
      const code = randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
      recoveryCodes.push({ code, used: false });
    }

    try {
      await this.db.update(users)
        .set({ recoveryCodes: this.stringifyJsonField(recoveryCodes) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error generating recovery codes:', error);
    }

    return codes;
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    // Find the recovery code
    const recoveryCodeIndex = user.recoveryCodes.findIndex(rc => rc.code === code && !rc.used);
    if (recoveryCodeIndex === -1) return false;

    // Mark the code as used
    user.recoveryCodes[recoveryCodeIndex].used = true;

    try {
      await this.db.update(users)
        .set({ recoveryCodes: this.stringifyJsonField(user.recoveryCodes) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error verifying recovery code:', error);
      return false;
    }

    return true;
  }

  // Custom Checkout Page methods
  async createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    try {
      const result = await this.db.insert(customCheckoutPages).values(insertPage).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating custom checkout page:', error);
      throw error;
    }
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.slug, slug)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page by slug:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    try {
      return await this.db.select().from(customCheckoutPages);
    } catch (error) {
      console.error('Error getting custom checkout pages:', error);
      return [];
    }
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.update(customCheckoutPages)
        .set(update)
        .where(eq(customCheckoutPages.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating custom checkout page:', error);
      return undefined;
    }
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ views: sql`${customCheckoutPages.views} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page views:', error);
    }
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ conversions: sql`${customCheckoutPages.conversions} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page conversions:', error);
    }
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(customCheckoutPages).where(eq(customCheckoutPages.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting custom checkout page:', error);
      return false;
    }
  }

  // Allowed Email methods
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      return await this.db.select().from(allowedEmails);
    } catch (error) {
      console.error('Error getting allowed emails:', error);
      return [];
    }
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting allowed email:', error);
      return undefined;
    }
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.email, email)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email by address:', error);
      return undefined;
    }
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    const allowedEmail = await this.getEmailByAddress(email);
    return !!allowedEmail;
  }

  async createAllowedEmail(insertEmail: InsertAllowedEmail): Promise<AllowedEmail> {
    try {
      const result = await this.db.insert(allowedEmails).values(insertEmail).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating allowed email:', error);
      throw error;
    }
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.update(allowedEmails)
        .set(update)
        .where(eq(allowedEmails.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating allowed email:', error);
      return undefined;
    }
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    const existing = await this.getEmailByAddress(emailAddress);
    if (existing) {
      const updated = await this.updateAllowedEmail(existing.id, update);
      return updated!;
    } else {
      return await this.createAllowedEmail({
        email: emailAddress,
        ...update,
        createdAt: new Date().toISOString()
      });
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(allowedEmails).where(eq(allowedEmails.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting allowed email:', error);
      return false;
    }
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        await this.createAllowedEmail({
          email,
          createdAt: new Date().toISOString()
        });
        success++;
      } catch (error) {
        console.error(`Error creating allowed email ${email}:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  // Email Template methods
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      return await this.db.select().from(emailTemplates);
    } catch (error) {
      console.error('Error getting email templates:', error);
      return [];
    }
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    try {
      const result = await this.db.select().from(emailTemplates).where(eq(emailTemplates.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email template:', error);
      return undefined;
    }
  }

  async createEmailTemplate(insertTemplate: InsertEmailTemplate): Promise<EmailTemplate> {
    try {
      const result = await this.db.insert(emailTemplates).values(insertTemplate).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating email template:', error);
      throw error;
    }
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    try {
      const result = await this.db.update(emailTemplates)
        .set(update)
        .where(eq(emailTemplates.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating email template:', error);
      return undefined;
    }
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(emailTemplates).where(eq(emailTemplates.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting email template:', error);
      return false;
    }
  }

  // PayPal Button methods
  async getPaypalButtons(): Promise<PaypalButton[]> {
    try {
      return await this.db.select().from(paypalButtons);
    } catch (error) {
      console.error('Error getting paypal buttons:', error);
      return [];
    }
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    try {
      const result = await this.db.select().from(paypalButtons).where(eq(paypalButtons.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting paypal button:', error);
      return undefined;
    }
  }

  async createPaypalButton(insertButton: InsertPaypalButton): Promise<PaypalButton> {
    try {
      const result = await this.db.insert(paypalButtons).values(insertButton).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating paypal button:', error);
      throw error;
    }
  }

  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> {
    try {
      const result = await this.db.update(paypalButtons)
        .set(update)
        .where(eq(paypalButtons.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating paypal button:', error);
      return undefined;
    }
  }

  async deletePaypalButton(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(paypalButtons).where(eq(paypalButtons.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting paypal button:', error);
      return false;
    }
  }

  // Custom Invoice methods
  async getCustomInvoices(): Promise<CustomInvoice[]> {
    try {
      return await this.db.select().from(customInvoices);
    } catch (error) {
      console.error('Error getting custom invoices:', error);
      return [];
    }
  }

  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> {
    try {
      const result = await this.db.select().from(customInvoices).where(eq(customInvoices.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom invoice:', error);
      return undefined;
    }
  }

  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> {
    try {
      const result = await this.db.select().from(customInvoices).where(eq(customInvoices.invoiceNumber, invoiceNumber)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom invoice by number:', error);
      return undefined;
    }
  }

  async createCustomInvoice(insertInvoice: InsertCustomInvoice): Promise<CustomInvoice> {
    try {
      const result = await this.db.insert(customInvoices).values(insertInvoice).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating custom invoice:', error);
      throw error;
    }
  }

  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> {
    try {
      const result = await this.db.update(customInvoices)
        .set(update)
        .where(eq(customInvoices.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating custom invoice:', error);
      return undefined;
    }
  }

  async incrementCustomInvoiceViewCount(id: number): Promise<void> {
    try {
      await this.db.update(customInvoices)
        .set({ viewCount: sql`${customInvoices.viewCount} + 1` })
        .where(eq(customInvoices.id, id));
    } catch (error) {
      console.error('Error incrementing custom invoice view count:', error);
    }
  }

  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> {
    try {
      const result = await this.db.update(customInvoices)
        .set({
          status: 'paid',
          paidAt: new Date().toISOString()
        })
        .where(eq(customInvoices.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error marking custom invoice as paid:', error);
      return undefined;
    }
  }

  async deleteCustomInvoice(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(customInvoices).where(eq(customInvoices.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting custom invoice:', error);
      return false;
    }
  }

  // Contact Inquiry methods (placeholder)
  async createContactInquiry(inquiry: any): Promise<any> {
    // This might need to be implemented based on how contact inquiries are stored
    return inquiry;
  }

  async getContactInquiries(): Promise<any[]> {
    // This might need to be implemented based on how contact inquiries are stored
    return [];
  }

  async updateContactInquiry(id: number, update: any): Promise<any> {
    // This might need to be implemented based on how contact inquiries are stored
    return update;
  }

  // Embed Code methods
  async getEmbedCodes(): Promise<EmbedCode[]> {
    try {
      return await this.db.select().from(embedCodes);
    } catch (error) {
      console.error('Error getting embed codes:', error);
      return [];
    }
  }

  async getEmbedCode(id: string): Promise<EmbedCode | undefined> {
    try {
      const result = await this.db.select().from(embedCodes).where(eq(embedCodes.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting embed code:', error);
      return undefined;
    }
  }

  async createEmbedCode(insertEmbedCode: EmbedCode): Promise<EmbedCode> {
    try {
      const result = await this.db.insert(embedCodes).values(insertEmbedCode).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating embed code:', error);
      throw error;
    }
  }

  async updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined> {
    try {
      const result = await this.db.update(embedCodes)
        .set(update)
        .where(eq(embedCodes.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating embed code:', error);
      return undefined;
    }
  }

  async deleteEmbedCode(id: string): Promise<boolean> {
    try {
      const result = await this.db.delete(embedCodes).where(eq(embedCodes.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting embed code:', error);
      return false;
    }
  }

  // SMTP Provider methods
  async getSmtpProviders(): Promise<SmtpProvider[]> {
    try {
      return await this.db.select().from(smtpProviders);
    } catch (error) {
      console.error('Error getting SMTP providers:', error);
      return [];
    }
  }

  async getSmtpProvider(id: string): Promise<SmtpProvider | undefined> {
    try {
      const result = await this.db.select().from(smtpProviders).where(eq(smtpProviders.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting SMTP provider:', error);
      return undefined;
    }
  }

  async createSmtpProvider(insertProvider: InsertSmtpProvider): Promise<SmtpProvider> {
    try {
      const result = await this.db.insert(smtpProviders).values(insertProvider).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating SMTP provider:', error);
      throw error;
    }
  }

  async updateSmtpProvider(id: string, update: Partial<InsertSmtpProvider>): Promise<SmtpProvider | undefined> {
    try {
      const result = await this.db.update(smtpProviders)
        .set(update)
        .where(eq(smtpProviders.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating SMTP provider:', error);
      return undefined;
    }
  }

  async deleteSmtpProvider(id: string): Promise<boolean> {
    try {
      const result = await this.db.delete(smtpProviders).where(eq(smtpProviders.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting SMTP provider:', error);
      return false;
    }
  }

  async getDefaultSmtpProvider(): Promise<SmtpProvider | undefined> {
    try {
      const result = await this.db.select().from(smtpProviders).where(eq(smtpProviders.isDefault, true)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting default SMTP provider:', error);
      return undefined;
    }
  }

  async setDefaultSmtpProvider(id: string): Promise<void> {
    try {
      // First, unset all defaults
      await this.db.update(smtpProviders).set({ isDefault: false });

      // Then set the new default
      await this.db.update(smtpProviders)
        .set({ isDefault: true })
        .where(eq(smtpProviders.id, id));
    } catch (error) {
      console.error('Error setting default SMTP provider:', error);
    }
  }

  // Missing methods that might be needed
  async getAllowedUsernames(): Promise<any[]> {
    // This might need to be implemented based on how allowed usernames are stored
    return [];
  }
}

// Create and export the storage instance
export const storage = new DatabaseStorage();
