#!/usr/bin/env tsx

import { db } from '../db';
import { sql } from 'drizzle-orm';

async function initializeDatabase() {
  console.log('🗄️ Initializing database tables...');
  
  try {
    // Create users table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        email TEXT,
        remember_me INTEGER NOT NULL DEFAULT 0,
        reset_token TEXT,
        reset_token_expiry TEXT,
        two_factor_secret TEXT,
        two_factor_enabled INTEGER NOT NULL DEFAULT 0,
        recovery_codes TEXT NOT NULL DEFAULT '[]',
        devices TEXT NOT NULL DEFAULT '[]'
      )
    `);
    console.log('✅ Created users table');

    // Create products table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created products table');

    // Create invoices table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        customer_name TEXT NOT NULL,
        customer_email TEXT NOT NULL,
        amount REAL NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        due_date TEXT,
        paid_at TEXT,
        notes TEXT
      )
    `);
    console.log('✅ Created invoices table');

    // Create custom_checkout_pages table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS custom_checkout_pages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        slug TEXT NOT NULL UNIQUE,
        product_name TEXT NOT NULL,
        product_description TEXT NOT NULL,
        price TEXT NOT NULL,
        image_url TEXT,
        payment_method TEXT NOT NULL,
        custom_payment_link_id TEXT,
        paypal_button_id TEXT,
        embed_code_id TEXT,
        require_allowed_email INTEGER NOT NULL DEFAULT 0,
        is_trial_checkout INTEGER NOT NULL DEFAULT 0,
        confirmation_message TEXT,
        header_title TEXT,
        footer_text TEXT,
        header_logo TEXT,
        footer_logo TEXT,
        theme_mode TEXT NOT NULL DEFAULT 'light',
        use_referrer_masking INTEGER NOT NULL DEFAULT 0,
        redirect_delay INTEGER NOT NULL DEFAULT 2000,
        expires_at TEXT,
        active INTEGER NOT NULL DEFAULT 1,
        views INTEGER NOT NULL DEFAULT 0,
        conversions INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created custom_checkout_pages table');

    // Create allowed_emails table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS allowed_emails (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT NOT NULL UNIQUE,
        notes TEXT,
        smtp_provider TEXT,
        last_updated TEXT,
        created_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created allowed_emails table');

    // Create email_templates table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS email_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        subject TEXT NOT NULL,
        html_content TEXT NOT NULL,
        text_content TEXT,
        content TEXT,
        category TEXT NOT NULL DEFAULT 'general',
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created email_templates table');

    // Create paypal_buttons table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS paypal_buttons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        button_code TEXT NOT NULL,
        description TEXT,
        active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created paypal_buttons table');

    // Create custom_invoices table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS custom_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        customer_name TEXT NOT NULL,
        customer_email TEXT NOT NULL,
        amount TEXT NOT NULL,
        currency TEXT NOT NULL DEFAULT 'USD',
        description TEXT NOT NULL,
        paypal_button_id INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        due_date TEXT,
        view_count INTEGER NOT NULL DEFAULT 0,
        paid_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created custom_invoices table');

    // Create smtp_providers table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS smtp_providers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        host TEXT NOT NULL,
        port TEXT NOT NULL,
        secure INTEGER NOT NULL DEFAULT 0,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        from_email TEXT NOT NULL,
        from_name TEXT NOT NULL,
        active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        is_backup INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created smtp_providers table');

    // Create embed_codes table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS embed_codes (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        platform TEXT NOT NULL,
        head_script TEXT NOT NULL,
        button_html TEXT NOT NULL,
        active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created embed_codes table');

    // Create system_messages table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS system_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT NOT NULL UNIQUE,
        category TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        content TEXT NOT NULL,
        is_html INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    console.log('✅ Created system_messages table');

    console.log('🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Error initializing database:', error);
    throw error;
  }
}

// Run the initialization
initializeDatabase().catch(console.error);
