{"version": "2.0", "exportDate": "2025-06-01T00:05:32.385Z", "metadata": {"description": "Complete default configuration export including all user customizations", "source": "local-development-setup"}, "data": {"customCheckoutPages": [], "allowedEmails": [], "smtpProviders": [{"id": "smtp-1748708256095", "name": "Smartonn", "host": "smtp-relay.brevo.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "6MZnj9VgkTHIyqvL", "fromEmail": "<EMAIL>", "fromName": "Smartonn", "active": 1, "isDefault": 0, "isBackup": 0, "createdAt": "2025-05-31T16:17:36.100Z", "updatedAt": "2025-05-31T22:56:28.474Z"}, {"id": "smtp-1748708327349", "name": "TVZYON", "host": "smtp-relay.brevo.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "AVG4zMQ7ysHPBOrY", "fromEmail": "<EMAIL>", "fromName": "TVZYON", "active": 1, "isDefault": 0, "isBackup": 0, "createdAt": "2025-05-31T16:18:47.348Z", "updatedAt": "2025-05-31T22:56:28.493Z"}, {"id": "smtp-1748708254251", "name": "AVIXIPTV", "host": "smtp-relay.brevo.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "zgBvM1jXWPL8YdhT", "fromEmail": "<EMAIL>", "fromName": "AVIXIPTV", "active": 1, "isDefault": 0, "isBackup": 0, "createdAt": "2025-05-31T16:17:36.100Z", "updatedAt": "2025-05-31T22:56:28.502Z"}, {"id": "smtp-1748708212541", "name": "DirectoIPTV", "host": "smtp-relay.brevo.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "VyLY1trZUHqMNFh2", "fromEmail": "<EMAIL>", "fromName": "DirectoIPTV", "active": 1, "isDefault": 0, "isBackup": 0, "createdAt": "2025-05-31T16:17:36.100Z", "updatedAt": "2025-05-31T22:56:28.511Z"}, {"id": "smtp-174870823251", "name": "Enzidswan", "host": "smtp-relay.brevo.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "6MZnj9VgkTHIyqvL", "fromEmail": "<EMAIL>", "fromName": "Enzidswan", "active": 1, "isDefault": 1, "isBackup": 1, "createdAt": "2025-05-31T16:17:36.100Z", "updatedAt": "2025-05-31T22:57:16.494Z"}], "emailTemplates": [], "paypalButtons": [{"name": "Basic PayPal Button", "buttonCode": "<form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_top\">\n<input type=\"hidden\" name=\"cmd\" value=\"_s-xclick\">\n<input type=\"hidden\" name=\"hosted_button_id\" value=\"SAMPLE123456\">\n<input type=\"image\" src=\"https://www.paypalobjects.com/en_US/i/btn/btn_buynowCC_LG.gif\" border=\"0\" name=\"submit\" alt=\"PayPal - The safer, easier way to pay online!\">\n<img alt=\"\" border=\"0\" src=\"https://www.paypalobjects.com/en_US/i/scr/pixel.gif\" width=\"1\" height=\"1\">\n</form>", "description": "Standard PayPal Buy Now button", "active": true, "createdAt": "2025-06-01T00:05:32.375Z", "updatedAt": "2025-06-01T00:05:32.376Z", "id": 1}, {"name": "Premium PayPal Button", "buttonCode": "<form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_top\">\n<input type=\"hidden\" name=\"cmd\" value=\"_s-xclick\">\n<input type=\"hidden\" name=\"hosted_button_id\" value=\"PREMIUM789012\">\n<input type=\"image\" src=\"https://www.paypalobjects.com/en_US/i/btn/btn_paynowCC_LG.gif\" border=\"0\" name=\"submit\" alt=\"PayPal - The safer, easier way to pay online!\">\n<img alt=\"\" border=\"0\" src=\"https://www.paypalobjects.com/en_US/i/scr/pixel.gif\" width=\"1\" height=\"1\">\n</form>", "description": "Premium PayPal Pay Now button", "active": true, "createdAt": "2025-06-01T00:05:32.376Z", "updatedAt": "2025-06-01T00:05:32.376Z", "id": 2}, {"name": "Subscription PayPal Button", "buttonCode": "<form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_top\">\n<input type=\"hidden\" name=\"cmd\" value=\"_s-xclick\">\n<input type=\"hidden\" name=\"hosted_button_id\" value=\"SUB345678\">\n<input type=\"image\" src=\"https://www.paypalobjects.com/en_US/i/btn/btn_subscribeCC_LG.gif\" border=\"0\" name=\"submit\" alt=\"PayPal - The safer, easier way to pay online!\">\n<img alt=\"\" border=\"0\" src=\"https://www.paypalobjects.com/en_US/i/scr/pixel.gif\" width=\"1\" height=\"1\">\n</form>", "description": "PayPal Subscription button", "active": true, "createdAt": "2025-06-01T00:05:32.376Z", "updatedAt": "2025-06-01T00:05:32.376Z", "id": 3}], "generalSettings": {"siteName": "Your Business Name", "siteDescription": "Your business description", "logoUrl": "", "faviconUrl": "", "primaryColor": "#0070ba", "secondaryColor": "#003087", "footerText": "© 2024 Your Business Name", "enableCheckout": true, "enableCustomCheckout": true, "enableTestMode": false, "defaultTestCustomer": {"enabled": false, "name": "", "email": ""}, "emailDomainRestriction": {"enabled": false, "allowedDomains": ""}, "seoPrivacy": {"globalNoIndex": true, "hideFromSearchEngines": true, "disableSitemaps": true, "hideFramework": true, "customRobotsTxt": "User-agent: *\nDisallow: /\n\n# Block all search engines and crawlers\nUser-agent: Googlebot\nDisallow: /\n\nUser-agent: Bingbot\nDisallow: /\n\nUser-agent: Slurp\nDisallow: /\n\nUser-agent: DuckDuckBot\nDisallow: /\n\nUser-agent: Baiduspider\nDisallow: /\n\nUser-agent: YandexBot\nDisallow: /\n\nUser-agent: facebookexternalhit\nDisallow: /\n\nUser-agent: Twitterbot\nDisallow: /\n\nUser-agent: LinkedInBot\nDisallow: /\n\nUser-agent: WhatsApp\nDisallow: /\n\nUser-agent: TelegramBot\nDisallow: /\n\n# Block SEO tools and analyzers\nUser-agent: AhrefsBot\nDisallow: /\n\nUser-agent: SemrushBot\nDisallow: /\n\nUser-agent: MJ12bot\nDisallow: /\n\nUser-agent: DotBot\nDisallow: /\n\nUser-agent: BLEXBot\nDisallow: /\n\nUser-agent: SiteAuditBot\nDisallow: /\n\nUser-agent: MegaIndex\nDisallow: /\n\nUser-agent: ScreamingFrogSEOSpider\nDisallow: /\n\nUser-agent: SeoSiteCheckup\nDisallow: /\n\nUser-agent: WooRankBot\nDisallow: /\n\nUser-agent: SEOkicks\nDisallow: /\n\nUser-agent: SEOlyticsCrawler\nDisallow: /\n\nUser-agent: LinkdexBot\nDisallow: /\n\nUser-agent: spbot\nDisallow: /\n\nUser-agent: MojeekBot\nDisallow: /\n\nUser-agent: PetalBot\nDisallow: /\n\nUser-agent: CCBot\nDisallow: /\n\nUser-agent: GPTBot\nDisallow: /\n\nUser-agent: ChatGPT-User\nDisallow: /\n\nUser-agent: Claude-Web\nDisallow: /\n\nUser-agent: anthropic-ai\nDisallow: /\n\nUser-agent: PerplexityBot\nDisallow: /\n\n# No sitemap provided\n# Sitemap:", "pageIndexingRules": {"homepage": false, "checkoutPages": false, "adminPages": false, "customPages": false}, "privacyHeaders": {"hideServerInfo": true, "preventFraming": true, "disableReferrer": true, "hideGenerator": true}}, "telegramBot": {"enabled": true, "botToken": "**********************************************", "adminChatId": "383368242", "webhookUrl": "https://00d6-196-74-45-154.ngrok-free.app/api/telegram/webhook", "notifications": {"newOrders": true, "paymentConfirmations": true, "trialUpgrades": true, "orderStatusChanges": true}, "emailIntegration": {"enabled": true, "allowQuickSend": true, "defaultTemplateId": "smartonn_template"}, "m3uManagement": {"enabled": true, "autoExtractCredentials": true, "credentialFormat": "Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}", "defaultM3uLinks": []}, "security": {"verifyAdminOnly": true, "rateLimitEnabled": true, "auditLogging": true}}}, "paymentConfig": {"providers": [{"id": "paypal", "name": "PayPal", "active": true, "config": {"clientId": "********************************************************************************", "clientSecret": "********************************************************************************", "mode": "sandbox", "webhookId": "", "paypalEmail": "<EMAIL>"}}, {"id": "custom-link", "name": "Custom Payment Links", "active": true, "config": {"links": [{"id": "link-1", "name": "Default Payment Required", "paymentLink": "https://example.com/pay", "buttonText": "Complete Payment", "successRedirectUrl": "https://example.com/thank-you", "active": true}], "rotationMethod": "round-robin", "lastUsedIndex": 0}}, {"id": "paypal-button-embed", "name": "PayPal <PERSON> Embed", "active": true, "config": {"buttons": [{"id": "button-1", "name": "De<PERSON><PERSON> Pay<PERSON>al <PERSON>", "buttonHtml": "\n<div style=\"text-align: center; margin: 20px 0;\">\n  <div style=\"max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 5px; padding: 15px; background-color: #fafafa;\">\n    <div style=\"font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;\">\n      Pay for {PRODUCT_NAME}\n    </div>\n    <div style=\"font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #0070ba;\">\n      {AMOUNT}\n    </div>\n    <div style=\"margin: 20px 0;\">\n      <a href=\"https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name={PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}\"\n         style=\"display: inline-block; padding: 12px 24px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;\">\n        Pay with PayPal\n      </a>\n    </div>\n    <div style=\"margin-top: 15px; font-size: 12px; color: #666;\">\n      Payment ID: {PAYMENT_ID}\n    </div>\n    <div style=\"margin-top: 10px;\">\n      <img src=\"https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png\" alt=\"PayPal Acceptance Mark\">\n    </div>\n  </div>\n</div>", "description": "Default PayPal button for testing", "active": true}], "rotationMethod": "round-robin", "lastUsedIndex": 0}}, {"id": "trial-custom-link", "name": "Trial Custom Payment Links", "active": true, "config": {"links": [{"id": "trial-link-1", "name": "Default Trial Payment Required", "paymentLink": "https://example.com/pay-trial", "buttonText": "Start Trial", "successRedirectUrl": "https://example.com/thank-you-trial", "active": true}, {"id": "trial-link-2", "name": "PayPal.me Trial Link", "paymentLink": "https://paypal.me/enzidswan/10", "buttonText": "Start Trial with PayPal", "successRedirectUrl": "https://example.com/trial-started", "active": true}, {"id": "trial-link-3", "name": "Stripe Trial Link", "paymentLink": "https://buy.stripe.com/test_trial", "buttonText": "Start Trial with <PERSON><PERSON>", "successRedirectUrl": "", "active": true}], "rotationMethod": "round-robin", "lastUsedIndex": 0}}, {"id": "trial-paypal-button-embed", "name": "Trial PayPal Button Embed", "active": true, "config": {"buttons": [{"id": "trial-button-1", "name": "Default Trial PayPal Button", "buttonHtml": "\n<div style=\"text-align: center; margin: 20px 0;\">\n  <div style=\"max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 5px; padding: 15px; background-color: #f5f8ff;\">\n    <div style=\"font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;\">\n      Start Trial for {PRODUCT_NAME}\n    </div>\n    <div style=\"font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #0070ba;\">\n      {AMOUNT}\n    </div>\n    <div style=\"margin: 20px 0;\">\n      <a href=\"https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}\"\n         style=\"display: inline-block; padding: 12px 24px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;\">\n        Start Trial with PayPal\n      </a>\n    </div>\n    <div style=\"margin-top: 15px; font-size: 12px; color: #666;\">\n      Payment ID: {PAYMENT_ID}\n    </div>\n    <div style=\"margin-top: 10px;\">\n      <img src=\"https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png\" alt=\"PayPal Acceptance Mark\">\n    </div>\n    <div style=\"margin-top: 10px; padding: 8px; background-color: #e6f7ff; border-radius: 4px; font-size: 12px; color: #0070ba;\">\n      This is a trial subscription. You can upgrade to a full subscription later.\n    </div>\n  </div>\n</div>", "description": "Default trial PayPal button for testing", "active": true}, {"id": "trial-button-2", "name": "Modern Trial PayPal Button", "buttonHtml": "\n<div style=\"text-align: center; margin: 20px 0;\">\n  <div style=\"max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; background-color: #f8faff; box-shadow: 0 4px 6px rgba(0,0,0,0.05);\">\n    <div style=\"font-size: 20px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;\">\n      Start Your Trial Today\n    </div>\n    <div style=\"font-size: 16px; margin-bottom: 15px; color: #4a5568;\">\n      {PRODUCT_NAME} - Limited Time Offer\n    </div>\n    <div style=\"font-size: 28px; font-weight: bold; margin-bottom: 20px; color: #0070ba;\">\n      {AMOUNT}\n    </div>\n    <div style=\"margin: 20px 0;\">\n      <a href=\"https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}\"\n         style=\"display: inline-block; padding: 14px 28px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 50px; font-weight: bold; font-size: 16px; transition: all 0.3s ease;\">\n        Begin Your Trial\n      </a>\n    </div>\n    <div style=\"margin-top: 15px; font-size: 12px; color: #666;\">\n      Secure Payment via PayPal - Order ID: {PAYMENT_ID}\n    </div>\n    <div style=\"margin-top: 10px;\">\n      <img src=\"https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png\" alt=\"PayPal Acceptance Mark\">\n    </div>\n    <div style=\"margin-top: 15px; padding: 10px; background-color: #e6f7ff; border-radius: 8px; font-size: 13px; color: #0070ba;\">\n      <strong>Trial Benefits:</strong> Full access to all features for a limited time. Upgrade anytime.\n    </div>\n  </div>\n</div>", "description": "Modern styled trial PayPal button with rounded corners", "active": true}, {"id": "trial-button-3", "name": "Minimalist Trial But<PERSON>", "buttonHtml": "\n<div style=\"text-align: center; margin: 20px 0;\">\n  <div style=\"max-width: 450px; margin: 0 auto; border: 1px solid #eaeaea; border-radius: 4px; padding: 20px; background-color: #ffffff;\">\n    <div style=\"font-size: 18px; font-weight: 500; margin-bottom: 10px; color: #333;\">\n      Try {PRODUCT_NAME}\n    </div>\n    <div style=\"font-size: 22px; font-weight: 600; margin-bottom: 20px; color: #0070ba;\">\n      {AMOUNT} Trial\n    </div>\n    <div style=\"margin: 20px 0;\">\n      <a href=\"https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}\"\n         style=\"display: inline-block; padding: 12px 24px; background-color: #333; color: white; text-decoration: none; border-radius: 3px; font-weight: 500; font-size: 15px;\">\n        Start Trial\n      </a>\n    </div>\n    <div style=\"margin-top: 15px; font-size: 12px; color: #777;\">\n      Secure payment processing by PayPal\n    </div>\n    <div style=\"margin-top: 10px;\">\n      <img src=\"https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-small.png\" alt=\"PayPal\">\n    </div>\n  </div>\n</div>", "description": "Minimalist trial button with clean design", "active": true}], "rotationMethod": "round-robin", "lastUsedIndex": 0}}]}, "uploadedImages": ["1748731557581-87fd69aebed3c434.png", "1748732677820-a3363f04c94320f1.png", "1748733109754-be201bb58ad8e96d.png", "1748733119219-30b8ff9c945b0afc.png", "1748733719394-5bb1aef8db23504c.png", "IPTV-12M.jpg", "IPTV-24h.jpg"]}}