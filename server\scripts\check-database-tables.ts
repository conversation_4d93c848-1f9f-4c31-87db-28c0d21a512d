#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';

async function checkDatabaseTables() {
  console.log('🔍 Checking database tables...');
  
  try {
    const sqlite = new Database('data.db');
    
    // Get all table names
    const tables = sqlite.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();
    
    console.log(`📊 Found ${tables.length} tables:`);
    
    for (const table of tables) {
      console.log(`  📋 ${table.name}`);
      
      // Get row count for each table
      try {
        const count = sqlite.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
        console.log(`     └─ ${count.count} rows`);
      } catch (error) {
        console.log(`     └─ Error counting rows: ${error.message}`);
      }
    }
    
    // Check specifically for custom checkout pages
    console.log('\n🔍 Checking for custom checkout pages...');
    try {
      const checkoutPages = sqlite.prepare(`
        SELECT COUNT(*) as count FROM custom_checkout_pages
      `).get();
      console.log(`✅ Found ${checkoutPages.count} custom checkout pages`);
    } catch (error) {
      console.log(`❌ Custom checkout pages table doesn't exist: ${error.message}`);
    }
    
    // Check for allowed emails
    console.log('\n🔍 Checking for allowed emails...');
    try {
      const allowedEmails = sqlite.prepare(`
        SELECT COUNT(*) as count FROM allowed_emails
      `).get();
      console.log(`✅ Found ${allowedEmails.count} allowed emails`);
    } catch (error) {
      console.log(`❌ Allowed emails table doesn't exist: ${error.message}`);
    }
    
    // Check for email templates
    console.log('\n🔍 Checking for email templates...');
    try {
      const emailTemplates = sqlite.prepare(`
        SELECT COUNT(*) as count FROM email_templates
      `).get();
      console.log(`✅ Found ${emailTemplates.count} email templates`);
    } catch (error) {
      console.log(`❌ Email templates table doesn't exist: ${error.message}`);
    }
    
    sqlite.close();
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  }
}

checkDatabaseTables().catch(console.error);
