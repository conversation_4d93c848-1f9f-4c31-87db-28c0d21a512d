#!/usr/bin/env tsx

import fetch from 'node-fetch';

async function checkCheckoutPagesViaAPI() {
  console.log('🔍 Checking for checkout pages via API...');
  
  try {
    // Try to access the admin API to get checkout pages
    const response = await fetch('http://localhost:3000/api/admin/custom-checkout', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // You might need to add authentication headers here
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Successfully fetched checkout pages from API');
      console.log(`📄 Found ${data.length} checkout pages:`);
      
      if (data.length > 0) {
        data.forEach((page: any, index: number) => {
          console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
          console.log(`      Product: ${page.productName}`);
          console.log(`      Price: $${page.price}`);
          console.log(`      Views: ${page.views || 0}, Conversions: ${page.conversions || 0}`);
          console.log('');
        });
        
        // Save the data to a file for export
        const fs = await import('fs/promises');
        const path = await import('path');
        
        const exportData = {
          version: '2.0',
          exportDate: new Date().toISOString(),
          source: 'api-export',
          customCheckoutPages: data
        };
        
        const configDir = path.join(process.cwd(), 'server', 'default-configs');
        
        // Create directory if it doesn't exist
        try {
          await fs.access(configDir);
        } catch {
          await fs.mkdir(configDir, { recursive: true });
        }
        
        const apiExportPath = path.join(configDir, 'api-checkout-pages-export.json');
        await fs.writeFile(apiExportPath, JSON.stringify(exportData, null, 2));
        
        console.log(`✅ Checkout pages exported to: ${apiExportPath}`);
        
        return data;
      } else {
        console.log('📄 No checkout pages found via API');
      }
    } else {
      console.log(`❌ API request failed: ${response.status} ${response.statusText}`);
      
      // Try without authentication
      console.log('🔄 Trying without authentication...');
      const publicResponse = await fetch('http://localhost:3000/api/custom-checkout', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (publicResponse.ok) {
        const data = await publicResponse.json();
        console.log('✅ Successfully fetched from public API');
        console.log(`📄 Found ${data.length} checkout pages`);
        return data;
      } else {
        console.log(`❌ Public API also failed: ${publicResponse.status} ${publicResponse.statusText}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking API:', error.message);
    
    // Check if the server is running
    try {
      const healthCheck = await fetch('http://localhost:3000/health', {
        method: 'GET',
        timeout: 5000
      });
      
      if (healthCheck.ok) {
        console.log('✅ Server is running, but checkout API is not accessible');
      } else {
        console.log('❌ Server health check failed');
      }
    } catch (healthError) {
      console.log('❌ Server appears to be down or not running on port 3000');
      console.log('💡 Please make sure your app is running with: npm run dev');
    }
  }
}

// Run the API check
checkCheckoutPagesViaAPI().catch(console.error);
