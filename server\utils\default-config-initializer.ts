import { db } from '../db';
import {
  customCheckoutPages,
  allowedEmails,
  smtpProviders,
  emailTemplates
} from '../../shared/schema';
import { generalConfigStorage } from '../general-config';
import { configStorage } from '../config-storage';
import { storage } from '../storage';
import { DefaultConfigExport } from './default-config-exporter';
import { eq } from 'drizzle-orm';
import fs from 'fs/promises';
import path from 'path';

export async function initializeDefaultConfiguration(): Promise<void> {
  console.log('🚀 Initializing default configuration...');
  
  try {
    // Load the default configuration
    const defaultConfig = await loadDefaultConfig();
    if (!defaultConfig) {
      console.log('⚠️ No default configuration found, skipping initialization');
      return;
    }

    console.log(`📦 Loading default configuration v${defaultConfig.version} from ${defaultConfig.exportDate}`);

    // Initialize custom checkout pages
    await initializeCheckoutPages(defaultConfig.data.customCheckoutPages);

    // Initialize allowed emails
    await initializeAllowedEmails(defaultConfig.data.allowedEmails);

    // Initialize SMTP providers
    await initializeSmtpProviders(defaultConfig.data.smtpProviders);

    // Initialize email templates
    await initializeEmailTemplates(defaultConfig.data.emailTemplates);

    // Initialize PayPal buttons
    await initializePaypalButtons(defaultConfig.data.paypalButtons);

    // Initialize uploaded images
    await initializeUploadedImages(defaultConfig.data.uploadedImages);

    // Update general settings
    await updateGeneralSettings(defaultConfig.data.generalSettings);

    // Update payment configuration
    await updatePaymentConfig(defaultConfig.data.paymentConfig);

    console.log('✅ Default configuration initialization completed successfully');

  } catch (error) {
    console.error('❌ Error initializing default configuration:', error);
    // Don't throw error to prevent app startup failure
  }
}

async function loadDefaultConfig(): Promise<DefaultConfigExport | null> {
  try {
    const configPath = path.join(process.cwd(), 'server', 'default-configs', 'default-configuration.json');
    const configData = await fs.readFile(configPath, 'utf-8');
    return JSON.parse(configData);
  } catch (error) {
    console.log('📁 Default configuration file not found');
    return null;
  }
}

async function initializeCheckoutPages(pages: any[]): Promise<void> {
  if (!pages || pages.length === 0) {
    console.log('📄 No default checkout pages to initialize');
    return;
  }

  console.log(`📄 Initializing ${pages.length} default checkout pages...`);

  for (const page of pages) {
    try {
      // Check if page already exists in memory storage
      const existingInMemory = await storage.getCustomCheckoutPageBySlug(page.slug);

      if (!existingInMemory) {
        // Add to memory storage (where the app actually stores checkout pages)
        const { id, ...pageData } = page;
        await storage.createCustomCheckoutPage({
          ...pageData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        console.log(`✅ Created default checkout page in memory: ${page.title}`);
      } else {
        console.log(`⏭️ Checkout page already exists in memory: ${page.title}`);
      }

      // Also try to add to database if tables exist
      try {
        const existing = await db.select()
          .from(customCheckoutPages)
          .where(eq(customCheckoutPages.slug, page.slug))
          .limit(1);

        if (existing.length === 0) {
          const { id, ...pageData } = page;
          await db.insert(customCheckoutPages).values({
            ...pageData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
          console.log(`✅ Created default checkout page in database: ${page.title}`);
        } else {
          console.log(`⏭️ Checkout page already exists in database: ${page.title}`);
        }
      } catch (dbError) {
        console.log(`⚠️ Could not add to database (table may not exist): ${page.title}`);
      }
    } catch (error) {
      console.error(`❌ Error creating checkout page ${page.title}:`, error);
    }
  }
}

async function initializeAllowedEmails(emails: any[]): Promise<void> {
  if (!emails || emails.length === 0) {
    console.log('📧 No default allowed emails to initialize');
    return;
  }

  console.log(`📧 Initializing ${emails.length} default allowed emails...`);
  
  for (const email of emails) {
    try {
      // Check if email already exists
      const existing = await db.select()
        .from(allowedEmails)
        .where(eq(allowedEmails.email, email.email))
        .limit(1);

      if (existing.length === 0) {
        const { id, ...emailData } = email;
        await db.insert(allowedEmails).values({
          ...emailData,
          createdAt: new Date().toISOString()
        });
        console.log(`✅ Added default allowed email: ${email.email}`);
      } else {
        console.log(`⏭️ Allowed email already exists: ${email.email}`);
      }
    } catch (error) {
      console.error(`❌ Error adding allowed email ${email.email}:`, error);
    }
  }
}

async function initializeSmtpProviders(providers: any[]): Promise<void> {
  if (!providers || providers.length === 0) {
    console.log('📮 No default SMTP providers to initialize');
    return;
  }

  console.log(`📮 Initializing ${providers.length} default SMTP providers...`);
  
  for (const provider of providers) {
    try {
      // Check if provider already exists
      const existing = await db.select()
        .from(smtpProviders)
        .where(eq(smtpProviders.id, provider.id))
        .limit(1);

      if (existing.length === 0) {
        await db.insert(smtpProviders).values({
          ...provider,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        console.log(`✅ Added default SMTP provider: ${provider.name}`);
      } else {
        console.log(`⏭️ SMTP provider already exists: ${provider.name}`);
      }
    } catch (error) {
      console.error(`❌ Error adding SMTP provider ${provider.name}:`, error);
    }
  }
}

async function initializeEmailTemplates(templates: any[]): Promise<void> {
  if (!templates || templates.length === 0) {
    console.log('📝 No default email templates to initialize');
    return;
  }

  console.log(`📝 Initializing ${templates.length} default email templates...`);
  
  for (const template of templates) {
    try {
      // Check if template already exists
      const existing = await db.select()
        .from(emailTemplates)
        .where(eq(emailTemplates.templateId, template.templateId))
        .limit(1);

      if (existing.length === 0) {
        const { id, ...templateData } = template;
        await db.insert(emailTemplates).values({
          ...templateData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        console.log(`✅ Added default email template: ${template.name}`);
      } else {
        console.log(`⏭️ Email template already exists: ${template.name}`);
      }
    } catch (error) {
      console.error(`❌ Error adding email template ${template.name}:`, error);
    }
  }
}

async function initializePaypalButtons(buttons: any[]): Promise<void> {
  if (!buttons || buttons.length === 0) {
    console.log('💳 No default PayPal buttons to initialize');
    return;
  }

  console.log(`💳 Initializing ${buttons.length} default PayPal buttons...`);

  for (const button of buttons) {
    try {
      // Add to memory storage (PayPal buttons are stored in memory)
      const existingButton = await storage.getPaypalButton(button.id);

      if (!existingButton) {
        const { id, ...buttonData } = button;
        await storage.createPaypalButton({
          ...buttonData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        console.log(`✅ Added default PayPal button: ${button.name}`);
      } else {
        console.log(`⏭️ PayPal button already exists: ${button.name}`);
      }
    } catch (error) {
      console.error(`❌ Error adding PayPal button ${button.name}:`, error);
    }
  }
}

async function initializeUploadedImages(images: string[]): Promise<void> {
  if (!images || images.length === 0) {
    console.log('🖼️ No default images to initialize');
    return;
  }

  console.log(`🖼️ Initializing ${images.length} default images...`);

  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const defaultImagesDir = path.join(process.cwd(), 'server', 'default-configs', 'images');

    // Create uploads directory if it doesn't exist
    try {
      await fs.access(uploadsDir);
    } catch {
      await fs.mkdir(uploadsDir, { recursive: true });
    }

    // Copy default images if they exist
    try {
      await fs.access(defaultImagesDir);

      for (const imageName of images) {
        const sourcePath = path.join(defaultImagesDir, imageName);
        const destPath = path.join(uploadsDir, imageName);

        try {
          // Check if image already exists
          await fs.access(destPath);
          console.log(`⏭️ Image already exists: ${imageName}`);
        } catch {
          // Copy the image
          await fs.copyFile(sourcePath, destPath);
          console.log(`✅ Copied default image: ${imageName}`);
        }
      }
    } catch {
      console.log('📁 No default images directory found, skipping image initialization');
    }

  } catch (error) {
    console.error('❌ Error initializing uploaded images:', error);
  }
}

async function updateGeneralSettings(settings: any): Promise<void> {
  if (!settings) {
    console.log('⚙️ No default general settings to apply');
    return;
  }

  console.log('⚙️ Applying default general settings...');

  try {
    // Update the general config storage with default values
    Object.assign(generalConfigStorage, settings);
    console.log('✅ General settings updated with defaults');
  } catch (error) {
    console.error('❌ Error updating general settings:', error);
  }
}

async function updatePaymentConfig(config: any): Promise<void> {
  if (!config) {
    console.log('💳 No default payment configuration to apply');
    return;
  }

  console.log('💳 Applying default payment configuration...');

  try {
    // Update the payment config storage with default values
    Object.assign(configStorage.payment, config);
    console.log('✅ Payment configuration updated with defaults');
  } catch (error) {
    console.error('❌ Error updating payment configuration:', error);
  }
}
