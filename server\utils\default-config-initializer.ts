import { storage } from '../storage';
import fs from 'fs/promises';
import path from 'path';

interface DefaultConfiguration {
  version: string;
  exportDate: string;
  data: {
    customCheckoutPages: any[];
    smtpProviders: any[];
    paypalButtons: any[];
    emailTemplates: any[];
    images: any[];
  };
}

/**
 * Initialize default configuration from JSON file into database
 */
export async function initializeDefaultConfiguration() {
  try {
    console.log('🚀 Initializing default configuration from JSON file...');
    
    // Load the default configuration
    const configPath = path.join(process.cwd(), 'server', 'default-configs', 'default-configuration.json');
    
    let configData: string;
    try {
      configData = await fs.readFile(configPath, 'utf-8');
    } catch (error) {
      console.log('📄 No default configuration file found, skipping initialization');
      return;
    }
    
    const config: DefaultConfiguration = JSON.parse(configData);
    console.log(`📦 Loading default configuration v${config.version} from ${config.exportDate}`);
    
    // Initialize checkout pages
    await initializeCheckoutPages(config.data.customCheckoutPages);
    
    // Initialize SMTP providers
    await initializeSmtpProviders(config.data.smtpProviders);
    
    // Initialize PayPal buttons
    await initializePaypalButtons(config.data.paypalButtons);
    
    // Initialize email templates
    await initializeEmailTemplates(config.data.emailTemplates);
    
    console.log('✅ Default configuration initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during default configuration initialization:', error);
  }
}

/**
 * Initialize checkout pages from default configuration
 */
async function initializeCheckoutPages(checkoutPages: any[]) {
  if (!checkoutPages || checkoutPages.length === 0) {
    console.log('📄 No default checkout pages to initialize');
    return;
  }
  
  console.log(`📄 Initializing ${checkoutPages.length} default checkout pages...`);
  
  // Check if pages already exist
  const existingPages = await storage.getCustomCheckoutPages();
  
  let createdCount = 0;
  for (const page of checkoutPages) {
    try {
      // Check if page with this slug already exists
      const existingPage = existingPages.find(p => p.slug === page.slug);
      
      if (existingPage) {
        console.log(`⏭️ Checkout page "${page.title}" already exists, skipping`);
        continue;
      }
      
      // Create the page
      const { id, ...pageData } = page;
      const createdPage = await storage.createCustomCheckoutPage({
        ...pageData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      console.log(`✅ Created default checkout page in database: ${createdPage.title}`);
      createdCount++;
      
    } catch (error) {
      console.error(`❌ Error creating checkout page ${page.title}:`, error);
    }
  }
  
  console.log(`📄 Checkout pages initialization completed: ${createdCount}/${checkoutPages.length} created`);
}

/**
 * Initialize SMTP providers from default configuration
 */
async function initializeSmtpProviders(smtpProviders: any[]) {
  if (!smtpProviders || smtpProviders.length === 0) {
    console.log('📮 No default SMTP providers to initialize');
    return;
  }
  
  console.log(`📮 Initializing ${smtpProviders.length} default SMTP providers...`);
  
  // Check if providers already exist
  const existingProviders = await storage.getSmtpProviders();
  
  let createdCount = 0;
  for (const provider of smtpProviders) {
    try {
      // Check if provider with this ID already exists
      const existingProvider = existingProviders.find(p => p.id === provider.id);
      
      if (existingProvider) {
        console.log(`⏭️ SMTP provider "${provider.name}" already exists, skipping`);
        continue;
      }
      
      // Create the provider
      const createdProvider = await storage.createSmtpProvider({
        ...provider,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      console.log(`✅ Created default SMTP provider in database: ${createdProvider.name}`);
      createdCount++;
      
    } catch (error) {
      console.error(`❌ Error creating SMTP provider ${provider.name}:`, error);
    }
  }
  
  console.log(`📮 SMTP providers initialization completed: ${createdCount}/${smtpProviders.length} created`);
}

/**
 * Initialize PayPal buttons from default configuration
 */
async function initializePaypalButtons(paypalButtons: any[]) {
  if (!paypalButtons || paypalButtons.length === 0) {
    console.log('💳 No default PayPal buttons to initialize');
    return;
  }
  
  console.log(`💳 Initializing ${paypalButtons.length} default PayPal buttons...`);
  
  // Check if buttons already exist
  const existingButtons = await storage.getPaypalButtons();
  
  let createdCount = 0;
  for (const button of paypalButtons) {
    try {
      // Check if button with this name already exists
      const existingButton = existingButtons.find(b => b.name === button.name);
      
      if (existingButton) {
        console.log(`⏭️ PayPal button "${button.name}" already exists, skipping`);
        continue;
      }
      
      // Create the button
      const { id, ...buttonData } = button;
      const createdButton = await storage.createPaypalButton({
        ...buttonData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      console.log(`✅ Created default PayPal button in database: ${createdButton.name}`);
      createdCount++;
      
    } catch (error) {
      console.error(`❌ Error creating PayPal button ${button.name}:`, error);
    }
  }
  
  console.log(`💳 PayPal buttons initialization completed: ${createdCount}/${paypalButtons.length} created`);
}

/**
 * Initialize email templates from default configuration
 */
async function initializeEmailTemplates(emailTemplates: any[]) {
  if (!emailTemplates || emailTemplates.length === 0) {
    console.log('📧 No default email templates to initialize');
    return;
  }
  
  console.log(`📧 Initializing ${emailTemplates.length} default email templates...`);
  
  // Check if templates already exist
  const existingTemplates = await storage.getEmailTemplates();
  
  let createdCount = 0;
  for (const template of emailTemplates) {
    try {
      // Check if template with this templateId already exists
      const existingTemplate = existingTemplates.find(t => t.templateId === template.templateId);
      
      if (existingTemplate) {
        console.log(`⏭️ Email template "${template.name}" already exists, skipping`);
        continue;
      }
      
      // Create the template
      const { id, ...templateData } = template;
      const createdTemplate = await storage.createEmailTemplate({
        ...templateData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      console.log(`✅ Created default email template in database: ${createdTemplate.name}`);
      createdCount++;
      
    } catch (error) {
      console.error(`❌ Error creating email template ${template.name}:`, error);
    }
  }
  
  console.log(`📧 Email templates initialization completed: ${createdCount}/${emailTemplates.length} created`);
}
