import Database from 'better-sqlite3';
import { join } from 'path';

const dbPath = join(process.cwd(), 'server', 'database.sqlite');
const db = new Database(dbPath);

console.log('🔄 Starting database migration...');

try {
  // Check if products table exists and has image_url column
  const productsTableInfo = db.prepare("PRAGMA table_info(products)").all() as any[];
  const hasImageUrl = productsTableInfo.some((col: any) => col.name === 'image_url');
  
  if (!hasImageUrl) {
    console.log('➕ Adding image_url column to products table...');
    db.exec(`ALTER TABLE products ADD COLUMN image_url TEXT NOT NULL DEFAULT ''`);
    console.log('✅ Added image_url column to products table');
  } else {
    console.log('⏭️ Products table already has image_url column');
  }

  // Check if invoices table exists and has product_id column
  const invoicesTableInfo = db.prepare("PRAGMA table_info(invoices)").all() as any[];
  const hasProductId = invoicesTableInfo.some((col: any) => col.name === 'product_id');
  
  if (!hasProductId) {
    console.log('➕ Adding product_id column to invoices table...');
    db.exec(`ALTER TABLE invoices ADD COLUMN product_id INTEGER NOT NULL DEFAULT 1`);
    console.log('✅ Added product_id column to invoices table');
  } else {
    console.log('⏭️ Invoices table already has product_id column');
  }

  // Check if users table has auto_login_enabled column
  const usersTableInfo = db.prepare("PRAGMA table_info(users)").all() as any[];
  const hasAutoLogin = usersTableInfo.some((col: any) => col.name === 'auto_login_enabled');
  
  if (!hasAutoLogin) {
    console.log('➕ Adding auto_login_enabled column to users table...');
    db.exec(`ALTER TABLE users ADD COLUMN auto_login_enabled INTEGER NOT NULL DEFAULT 0`);
    console.log('✅ Added auto_login_enabled column to users table');
  } else {
    console.log('⏭️ Users table already has auto_login_enabled column');
  }

  console.log('✅ Database migration completed successfully!');
} catch (error) {
  console.error('❌ Database migration failed:', error);
  process.exit(1);
} finally {
  db.close();
}
