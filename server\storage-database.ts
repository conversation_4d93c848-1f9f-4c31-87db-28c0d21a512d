import {
  users,
  products,
  invoices,
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  smtpProviders,
  embedCodes,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice,
  type SmtpProvider,
  type InsertSmtpProvider,
  type EmbedCode,
  type InsertEmbedCode
} from "@shared/schema";
import { createHash, randomBytes } from 'crypto';
import { db } from './db';
import { eq, sql } from 'drizzle-orm';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

// Recovery code interface
interface RecoveryCode {
  code: string;
  used: boolean;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyUserCredentials(username: string, password: string): Promise<boolean>;
  verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean>;
  saveResetToken(userId: number, token: string, expiry: Date): Promise<void>;
  validateResetToken(token: string): Promise<boolean>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  updateUserPassword(userId: number, password: string): Promise<void>;
  clearResetToken(userId: number): Promise<void>;
  updateUsername(userId: number, username: string): Promise<void>;
  updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void>;
  enableTwoFactor(userId: number, secret: string): Promise<void>;
  disableTwoFactor(userId: number): Promise<void>;
  verifyTwoFactorToken(userId: number, token: string): Promise<boolean>;

  // Recovery code methods
  generateRecoveryCodes(userId: number): Promise<string[]>;
  verifyRecoveryCode(userId: number, code: string): Promise<boolean>;

  // Device tracking methods
  addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device>;
  getDevices(userId: number): Promise<Device[]>;
  updateDeviceLastLogin(userId: number, deviceId: string): Promise<void>;
  removeDevice(userId: number, deviceId: string): Promise<boolean>;

  // Product methods
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  createProduct(product: InsertProduct): Promise<Product>;

  // Invoice methods
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined>;

  // Configuration methods
  getGeneralSettings(): Promise<any>;
  getEmailConfig(): Promise<any>;
  getPaymentConfig(): Promise<any>;

  // Custom Checkout Page methods
  createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage>;
  getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPages(): Promise<CustomCheckoutPage[]>;
  updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined>;
  incrementCustomCheckoutPageViews(id: number): Promise<void>;
  incrementCustomCheckoutPageConversions(id: number): Promise<void>;
  deleteCustomCheckoutPage(id: number): Promise<boolean>;

  // Allowed Email methods
  getAllowedEmails(): Promise<AllowedEmail[]>;
  getAllowedEmail(id: number): Promise<AllowedEmail | undefined>;
  getEmailByAddress(email: string): Promise<AllowedEmail | undefined>;
  isEmailAllowed(email: string): Promise<boolean>;
  createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail>;
  updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined>;
  updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail>;
  deleteAllowedEmail(id: number): Promise<boolean>;
  bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }>;

  // Email Template methods
  getEmailTemplates(): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;
  deleteEmailTemplate(id: number): Promise<boolean>;

  // PayPal Button methods
  getPaypalButtons(): Promise<PaypalButton[]>;
  getPaypalButton(id: number): Promise<PaypalButton | undefined>;
  createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton>;
  updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined>;
  deletePaypalButton(id: number): Promise<boolean>;

  // Custom Invoice methods
  getCustomInvoices(): Promise<CustomInvoice[]>;
  getCustomInvoice(id: number): Promise<CustomInvoice | undefined>;
  getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined>;
  createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice>;
  updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined>;
  incrementCustomInvoiceViewCount(id: number): Promise<void>;
  markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined>;
  deleteCustomInvoice(id: number): Promise<boolean>;

  // Contact Inquiry methods
  createContactInquiry?(inquiry: any): Promise<any>;
  getContactInquiries?(): Promise<any[]>;
  updateContactInquiry?(id: number, update: any): Promise<any>;

  // Embed Code methods
  getEmbedCodes(): Promise<EmbedCode[]>;
  getEmbedCode(id: string): Promise<EmbedCode | undefined>;
  createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode>;
  updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined>;
  deleteEmbedCode(id: string): Promise<boolean>;

  // SMTP Provider methods
  getSmtpProviders(): Promise<SmtpProvider[]>;
  getSmtpProvider(id: string): Promise<SmtpProvider | undefined>;
  createSmtpProvider(provider: InsertSmtpProvider): Promise<SmtpProvider>;
  updateSmtpProvider(id: string, update: Partial<InsertSmtpProvider>): Promise<SmtpProvider | undefined>;
  deleteSmtpProvider(id: string): Promise<boolean>;
  getDefaultSmtpProvider(): Promise<SmtpProvider | undefined>;
  setDefaultSmtpProvider(id: string): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  private db: typeof db;

  constructor() {
    this.db = db;
    console.log('🗄️ DatabaseStorage initialized - all data will be stored in database');
  }

  // Helper method to parse JSON fields safely
  private parseJsonField<T>(field: string | null, defaultValue: T): T {
    if (!field) return defaultValue;
    try {
      return JSON.parse(field);
    } catch {
      return defaultValue;
    }
  }

  // Helper method to stringify JSON fields
  private stringifyJsonField<T>(value: T): string {
    return JSON.stringify(value);
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
      if (!result[0]) return undefined;
      
      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.username, username)).limit(1);
      if (!result[0]) return undefined;
      
      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
      if (!result[0]) return undefined;
      
      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user by email:', error);
      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      // Hash the password if it's not already hashed
      let password = insertUser.password;
      if (!password.match(/^[0-9a-f]{64}$/i)) {
        password = createHash('sha256').update(password).digest('hex');
      }

      const userData = {
        ...insertUser,
        password,
        rememberMe: insertUser.rememberMe || false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: undefined,
        twoFactorEnabled: false,
        recoveryCodes: this.stringifyJsonField([]),
        devices: this.stringifyJsonField([])
      };

      const result = await this.db.insert(users).values(userData).returning();
      const user = result[0];
      
      return {
        ...user,
        recoveryCodes: [],
        devices: []
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          resetToken: token,
          resetTokenExpiry: expiry.toISOString()
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error saving reset token:', error);
    }
  }

  async validateResetToken(token: string): Promise<boolean> {
    const user = await this.getUserByResetToken(token);
    if (!user) return false;

    // Check if token is expired
    if (new Date() > new Date(user.resetTokenExpiry!)) {
      return false;
    }

    return true;
  }

  async getUserByResetToken(token: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.resetToken, token)).limit(1);
      if (!result[0]) return undefined;

      const user = result[0];
      return {
        ...user,
        recoveryCodes: this.parseJsonField(user.recoveryCodes, []),
        devices: this.parseJsonField(user.devices, [])
      };
    } catch (error) {
      console.error('Error getting user by reset token:', error);
      return undefined;
    }
  }

  async updateUserPassword(userId: number, password: string): Promise<void> {
    try {
      await this.db.update(users)
        .set({ password })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating user password:', error);
    }
  }

  async clearResetToken(userId: number): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          resetToken: null,
          resetTokenExpiry: null
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error clearing reset token:', error);
    }
  }

  async updateUsername(userId: number, username: string): Promise<void> {
    try {
      await this.db.update(users)
        .set({ username })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating username:', error);
    }
  }

  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {
    try {
      await this.db.update(users)
        .set({ rememberMe })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating auto login settings:', error);
    }
  }

  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          twoFactorSecret: secret,
          twoFactorEnabled: true
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error enabling two factor:', error);
    }
  }

  async disableTwoFactor(userId: number): Promise<void> {
    try {
      await this.db.update(users)
        .set({
          twoFactorSecret: null,
          twoFactorEnabled: false
        })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error disabling two factor:', error);
    }
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) return false;

    // The actual verification is done in the auth routes using otplib
    // This method is just a placeholder for the interface
    return true;
  }

  // Recovery code methods
  async generateRecoveryCodes(userId: number): Promise<string[]> {
    const user = await this.getUser(userId);
    if (!user) return [];

    // Generate 10 recovery codes
    const codes: string[] = [];
    const recoveryCodes: RecoveryCode[] = [];

    for (let i = 0; i < 10; i++) {
      // Generate a random 8-character code
      const code = randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
      recoveryCodes.push({ code, used: false });
    }

    try {
      await this.db.update(users)
        .set({ recoveryCodes: this.stringifyJsonField(recoveryCodes) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error generating recovery codes:', error);
    }

    return codes;
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    // Find the recovery code
    const recoveryCodeIndex = user.recoveryCodes.findIndex(rc => rc.code === code && !rc.used);
    if (recoveryCodeIndex === -1) return false;

    // Mark the code as used
    user.recoveryCodes[recoveryCodeIndex].used = true;

    try {
      await this.db.update(users)
        .set({ recoveryCodes: this.stringifyJsonField(user.recoveryCodes) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error verifying recovery code:', error);
      return false;
    }

    return true;
  }

  // Device tracking methods
  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    const user = await this.getUser(userId);
    if (!user) throw new Error('User not found');

    const now = new Date().toISOString();
    const device: Device = {
      ...deviceInfo,
      id: randomBytes(16).toString('hex'),
      createdAt: now,
      lastLogin: now
    };

    user.devices.push(device);

    try {
      await this.db.update(users)
        .set({ devices: this.stringifyJsonField(user.devices) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error adding device:', error);
      throw error;
    }

    return device;
  }

  async getDevices(userId: number): Promise<Device[]> {
    const user = await this.getUser(userId);
    if (!user) return [];
    return user.devices;
  }

  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    const deviceIndex = user.devices.findIndex(d => d.id === deviceId);
    if (deviceIndex === -1) return;

    user.devices[deviceIndex].lastLogin = new Date().toISOString();

    try {
      await this.db.update(users)
        .set({ devices: this.stringifyJsonField(user.devices) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error updating device last login:', error);
    }
  }

  async removeDevice(userId: number, deviceId: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    const initialLength = user.devices.length;
    user.devices = user.devices.filter(d => d.id !== deviceId);

    if (user.devices.length === initialLength) {
      return false; // No device was removed
    }

    try {
      await this.db.update(users)
        .set({ devices: this.stringifyJsonField(user.devices) })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error('Error removing device:', error);
      return false;
    }

    return true;
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    try {
      return await this.db.select().from(products);
    } catch (error) {
      console.error('Error getting products:', error);
      return [];
    }
  }

  async getProduct(id: number): Promise<Product | undefined> {
    try {
      const result = await this.db.select().from(products).where(eq(products.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting product:', error);
      return undefined;
    }
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    try {
      const result = await this.db.insert(products).values(insertProduct).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  // Invoice methods
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    try {
      console.log('Creating invoice with data:', insertInvoice);
      const result = await this.db.insert(invoices).values(insertInvoice).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    try {
      const result = await this.db.select().from(invoices).where(eq(invoices.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting invoice:', error);
      return undefined;
    }
  }

  async getInvoices(): Promise<Invoice[]> {
    try {
      return await this.db.select().from(invoices);
    } catch (error) {
      console.error('Error getting invoices:', error);
      return [];
    }
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    try {
      const result = await this.db.update(invoices)
        .set(update)
        .where(eq(invoices.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating invoice:', error);
      return undefined;
    }
  }

  // Configuration methods (placeholder - these might be stored differently)
  async getGeneralSettings(): Promise<any> {
    // This might need to be implemented based on how general settings are stored
    return {};
  }

  async getEmailConfig(): Promise<any> {
    // This might need to be implemented based on how email config is stored
    return {};
  }

  async getPaymentConfig(): Promise<any> {
    // This might need to be implemented based on how payment config is stored
    return {};
  }
