#!/usr/bin/env tsx

import { storage } from '../storage';
import fs from 'fs/promises';
import path from 'path';

async function exportMemoryStorageData() {
  console.log('🔍 Exporting in-memory storage data...');
  
  try {
    // Export custom checkout pages from memory storage
    console.log('📄 Exporting custom checkout pages from memory...');
    const checkoutPages = await storage.getCustomCheckoutPages();
    console.log(`✅ Found ${checkoutPages.length} custom checkout pages in memory`);
    
    if (checkoutPages.length > 0) {
      console.log('📋 Custom Checkout Pages:');
      checkoutPages.forEach((page, index) => {
        console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
        console.log(`      Product: ${page.productName}`);
        console.log(`      Price: $${page.price}`);
        console.log(`      Payment Method: ${page.paymentMethod}`);
        console.log(`      Views: ${page.views}, Conversions: ${page.conversions}`);
        console.log(`      Trial: ${page.isTrialCheckout ? 'Yes' : 'No'}`);
        console.log('');
      });
    }
    
    // Export allowed emails from memory storage
    console.log('📧 Exporting allowed emails from memory...');
    const allowedEmails = await storage.getAllowedEmails();
    console.log(`✅ Found ${allowedEmails.length} allowed emails in memory`);
    
    if (allowedEmails.length > 0) {
      console.log('📧 Allowed Emails:');
      allowedEmails.forEach((email, index) => {
        console.log(`   ${index + 1}. ${email.email} (${email.domain})`);
      });
      console.log('');
    }
    
    // Export email templates from memory storage
    console.log('📝 Exporting email templates from memory...');
    const emailTemplates = await storage.getEmailTemplates();
    console.log(`✅ Found ${emailTemplates.length} email templates in memory`);
    
    if (emailTemplates.length > 0) {
      console.log('📝 Email Templates:');
      emailTemplates.forEach((template, index) => {
        console.log(`   ${index + 1}. ${template.name} (${template.templateId})`);
        console.log(`      Subject: ${template.subject}`);
      });
      console.log('');
    }
    
    // Export PayPal buttons from memory storage
    console.log('💳 Exporting PayPal buttons from memory...');
    const paypalButtons = await storage.getPaypalButtons();
    console.log(`✅ Found ${paypalButtons.length} PayPal buttons in memory`);
    
    if (paypalButtons.length > 0) {
      console.log('💳 PayPal Buttons:');
      paypalButtons.forEach((button, index) => {
        console.log(`   ${index + 1}. ${button.name} (ID: ${button.id})`);
        console.log(`      Amount: $${button.amount}`);
        console.log(`      Currency: ${button.currency}`);
      });
      console.log('');
    }
    
    // Create export data
    const exportData = {
      version: '2.0',
      exportDate: new Date().toISOString(),
      source: 'memory-storage',
      data: {
        customCheckoutPages: checkoutPages,
        allowedEmails: allowedEmails,
        emailTemplates: emailTemplates,
        paypalButtons: paypalButtons
      }
    };
    
    // Save to file
    const configDir = path.join(process.cwd(), 'server', 'default-configs');
    
    // Create directory if it doesn't exist
    try {
      await fs.access(configDir);
    } catch {
      await fs.mkdir(configDir, { recursive: true });
    }
    
    const memoryExportPath = path.join(configDir, 'memory-storage-export.json');
    await fs.writeFile(memoryExportPath, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ Memory storage data exported to: ${memoryExportPath}`);
    
    // Summary
    console.log('=====================================');
    console.log('📊 MEMORY STORAGE EXPORT SUMMARY:');
    console.log(`   📄 Custom Checkout Pages: ${checkoutPages.length}`);
    console.log(`   📧 Allowed Emails: ${allowedEmails.length}`);
    console.log(`   📝 Email Templates: ${emailTemplates.length}`);
    console.log(`   💳 PayPal Buttons: ${paypalButtons.length}`);
    console.log('=====================================');
    
    return exportData;
    
  } catch (error) {
    console.error('❌ Error exporting memory storage data:', error);
    throw error;
  }
}

// Run the export
exportMemoryStorageData().catch(console.error);
