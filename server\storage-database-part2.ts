// Part 2 of DatabaseStorage - Custom Checkout Pages and other methods
import { eq, sql } from 'drizzle-orm';
import {
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  smtpProviders,
  embedCodes,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice,
  type SmtpProvider,
  type InsertSmtpProvider,
  type EmbedCode,
  type InsertEmbedCode
} from "@shared/schema";

export class DatabaseStoragePart2 {
  private db: any;

  constructor(db: any) {
    this.db = db;
  }

  // Custom Checkout Page methods
  async createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    try {
      const result = await this.db.insert(customCheckoutPages).values(insertPage).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating custom checkout page:', error);
      throw error;
    }
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.select().from(customCheckoutPages).where(eq(customCheckoutPages.slug, slug)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page by slug:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    try {
      return await this.db.select().from(customCheckoutPages);
    } catch (error) {
      console.error('Error getting custom checkout pages:', error);
      return [];
    }
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await this.db.update(customCheckoutPages)
        .set(update)
        .where(eq(customCheckoutPages.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating custom checkout page:', error);
      return undefined;
    }
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ views: sql`${customCheckoutPages.views} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page views:', error);
    }
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    try {
      await this.db.update(customCheckoutPages)
        .set({ conversions: sql`${customCheckoutPages.conversions} + 1` })
        .where(eq(customCheckoutPages.id, id));
    } catch (error) {
      console.error('Error incrementing custom checkout page conversions:', error);
    }
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(customCheckoutPages).where(eq(customCheckoutPages.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting custom checkout page:', error);
      return false;
    }
  }

  // Allowed Email methods
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      return await this.db.select().from(allowedEmails);
    } catch (error) {
      console.error('Error getting allowed emails:', error);
      return [];
    }
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting allowed email:', error);
      return undefined;
    }
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.select().from(allowedEmails).where(eq(allowedEmails.email, email)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email by address:', error);
      return undefined;
    }
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    const allowedEmail = await this.getEmailByAddress(email);
    return !!allowedEmail;
  }

  async createAllowedEmail(insertEmail: InsertAllowedEmail): Promise<AllowedEmail> {
    try {
      const result = await this.db.insert(allowedEmails).values(insertEmail).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating allowed email:', error);
      throw error;
    }
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    try {
      const result = await this.db.update(allowedEmails)
        .set(update)
        .where(eq(allowedEmails.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating allowed email:', error);
      return undefined;
    }
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    const existing = await this.getEmailByAddress(emailAddress);
    if (existing) {
      const updated = await this.updateAllowedEmail(existing.id, update);
      return updated!;
    } else {
      return await this.createAllowedEmail({
        email: emailAddress,
        ...update,
        createdAt: new Date().toISOString()
      });
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(allowedEmails).where(eq(allowedEmails.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting allowed email:', error);
      return false;
    }
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        await this.createAllowedEmail({
          email,
          createdAt: new Date().toISOString()
        });
        success++;
      } catch (error) {
        console.error(`Error creating allowed email ${email}:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  // Email Template methods
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    try {
      return await this.db.select().from(emailTemplates);
    } catch (error) {
      console.error('Error getting email templates:', error);
      return [];
    }
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    try {
      const result = await this.db.select().from(emailTemplates).where(eq(emailTemplates.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email template:', error);
      return undefined;
    }
  }

  async createEmailTemplate(insertTemplate: InsertEmailTemplate): Promise<EmailTemplate> {
    try {
      const result = await this.db.insert(emailTemplates).values(insertTemplate).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating email template:', error);
      throw error;
    }
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    try {
      const result = await this.db.update(emailTemplates)
        .set(update)
        .where(eq(emailTemplates.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating email template:', error);
      return undefined;
    }
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(emailTemplates).where(eq(emailTemplates.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting email template:', error);
      return false;
    }
  }

  // PayPal Button methods
  async getPaypalButtons(): Promise<PaypalButton[]> {
    try {
      return await this.db.select().from(paypalButtons);
    } catch (error) {
      console.error('Error getting paypal buttons:', error);
      return [];
    }
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    try {
      const result = await this.db.select().from(paypalButtons).where(eq(paypalButtons.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting paypal button:', error);
      return undefined;
    }
  }

  async createPaypalButton(insertButton: InsertPaypalButton): Promise<PaypalButton> {
    try {
      const result = await this.db.insert(paypalButtons).values(insertButton).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating paypal button:', error);
      throw error;
    }
  }

  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> {
    try {
      const result = await this.db.update(paypalButtons)
        .set(update)
        .where(eq(paypalButtons.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating paypal button:', error);
      return undefined;
    }
  }

  async deletePaypalButton(id: number): Promise<boolean> {
    try {
      const result = await this.db.delete(paypalButtons).where(eq(paypalButtons.id, id));
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting paypal button:', error);
      return false;
    }
  }
}
