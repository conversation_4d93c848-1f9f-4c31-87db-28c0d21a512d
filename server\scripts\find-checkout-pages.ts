#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as schema from '../../shared/schema';
import { storage } from '../storage';
import fs from 'fs/promises';
import path from 'path';

async function findCheckoutPages() {
  console.log('🔍 Comprehensive search for custom checkout pages...');
  
  try {
    // 1. Check memory storage
    console.log('\n📄 Checking memory storage...');
    const memoryPages = await storage.getCustomCheckoutPages();
    console.log(`Found ${memoryPages.length} pages in memory storage`);
    if (memoryPages.length > 0) {
      memoryPages.forEach((page, index) => {
        console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
      });
    }
    
    // 2. Check main database (data.db)
    console.log('\n📄 Checking main database (data.db)...');
    try {
      const sqlite = new Database('data.db');
      const db = drizzle(sqlite, { schema });
      
      // Check if table exists
      const tables = sqlite.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='custom_checkout_pages'
      `).all();
      
      if (tables.length > 0) {
        console.log('✅ custom_checkout_pages table exists in data.db');
        const pages = await db.select().from(schema.customCheckoutPages);
        console.log(`Found ${pages.length} pages in data.db`);
        if (pages.length > 0) {
          pages.forEach((page, index) => {
            console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
          });
        }
      } else {
        console.log('❌ custom_checkout_pages table does not exist in data.db');
      }
      
      sqlite.close();
    } catch (error) {
      console.log(`❌ Error checking data.db: ${error.message}`);
    }
    
    // 3. Check server database (server/data/database.sqlite)
    console.log('\n📄 Checking server database (server/data/database.sqlite)...');
    try {
      const serverDbPath = path.join(process.cwd(), 'server', 'data', 'database.sqlite');
      await fs.access(serverDbPath);
      
      const sqlite = new Database(serverDbPath);
      const db = drizzle(sqlite, { schema });
      
      // Check if table exists
      const tables = sqlite.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='custom_checkout_pages'
      `).all();
      
      if (tables.length > 0) {
        console.log('✅ custom_checkout_pages table exists in server/data/database.sqlite');
        const pages = await db.select().from(schema.customCheckoutPages);
        console.log(`Found ${pages.length} pages in server/data/database.sqlite`);
        if (pages.length > 0) {
          pages.forEach((page, index) => {
            console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
          });
        }
      } else {
        console.log('❌ custom_checkout_pages table does not exist in server/data/database.sqlite');
      }
      
      sqlite.close();
    } catch (error) {
      console.log(`❌ Error checking server/data/database.sqlite: ${error.message}`);
    }
    
    // 4. Search for any other .db or .sqlite files
    console.log('\n📄 Searching for other database files...');
    try {
      const files = await fs.readdir(process.cwd());
      const dbFiles = files.filter(file => 
        file.endsWith('.db') || file.endsWith('.sqlite') || file.endsWith('.sqlite3')
      );
      
      console.log(`Found database files: ${dbFiles.join(', ')}`);
      
      for (const dbFile of dbFiles) {
        if (dbFile !== 'data.db') {
          try {
            console.log(`\n🔍 Checking ${dbFile}...`);
            const sqlite = new Database(dbFile);
            
            // Check if table exists
            const tables = sqlite.prepare(`
              SELECT name FROM sqlite_master 
              WHERE type='table' AND name='custom_checkout_pages'
            `).all();
            
            if (tables.length > 0) {
              console.log(`✅ custom_checkout_pages table exists in ${dbFile}`);
              const db = drizzle(sqlite, { schema });
              const pages = await db.select().from(schema.customCheckoutPages);
              console.log(`Found ${pages.length} pages in ${dbFile}`);
              if (pages.length > 0) {
                pages.forEach((page, index) => {
                  console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
                });
              }
            } else {
              console.log(`❌ custom_checkout_pages table does not exist in ${dbFile}`);
            }
            
            sqlite.close();
          } catch (error) {
            console.log(`❌ Error checking ${dbFile}: ${error.message}`);
          }
        }
      }
    } catch (error) {
      console.log(`❌ Error searching for database files: ${error.message}`);
    }
    
    // 5. Check if there are any JSON files with checkout page data
    console.log('\n📄 Searching for JSON configuration files...');
    try {
      const searchDirs = ['.', 'server', 'server/data', 'server/config'];
      
      for (const dir of searchDirs) {
        try {
          const files = await fs.readdir(dir);
          const jsonFiles = files.filter(file => file.endsWith('.json'));
          
          for (const jsonFile of jsonFiles) {
            try {
              const filePath = path.join(dir, jsonFile);
              const content = await fs.readFile(filePath, 'utf-8');
              const data = JSON.parse(content);
              
              // Check if this JSON contains checkout page data
              if (data.customCheckoutPages || data.checkoutPages || 
                  (Array.isArray(data) && data.some(item => item.slug || item.productName))) {
                console.log(`📄 Found potential checkout page data in: ${filePath}`);
                if (data.customCheckoutPages) {
                  console.log(`   Contains ${data.customCheckoutPages.length} checkout pages`);
                } else if (data.checkoutPages) {
                  console.log(`   Contains ${data.checkoutPages.length} checkout pages`);
                } else if (Array.isArray(data)) {
                  console.log(`   Contains ${data.length} items (potential checkout pages)`);
                }
              }
            } catch (error) {
              // Skip files that aren't valid JSON
            }
          }
        } catch (error) {
          // Skip directories that don't exist
        }
      }
    } catch (error) {
      console.log(`❌ Error searching for JSON files: ${error.message}`);
    }
    
    console.log('\n🔍 Search completed!');
    
  } catch (error) {
    console.error('❌ Error during search:', error);
  }
}

// Run the search
findCheckoutPages().catch(console.error);
