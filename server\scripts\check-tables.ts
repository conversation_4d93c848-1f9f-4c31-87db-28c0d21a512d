import Database from 'better-sqlite3';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const dbPath = join(__dirname, '..', 'database.sqlite');
console.log('📁 Database path:', dbPath);

const db = new Database(dbPath);
console.log('🔍 Checking database tables...');

try {
  // Get all tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all() as any[];

  console.log('📋 Existing tables:');
  if (tables.length === 0) {
    console.log('  No tables found in database');
  } else {

  tables.forEach((table: any) => {
    console.log(`  - ${table.name}`);
  });

  // Check each table structure
  for (const table of tables) {
    if (table.name.startsWith('sqlite_')) continue; // Skip system tables
    
    console.log(`\n📊 Structure of ${table.name}:`);
    const columns = db.prepare(`PRAGMA table_info(${table.name})`).all() as any[];
    columns.forEach((col: any) => {
      console.log(`  - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
    });
  }
  }

} catch (error) {
  console.error('❌ Error checking database:', error);
} finally {
  db.close();
}
