import express, { type Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import session from "express-session";
import createMemoryStore from "memorystore";
import { storage } from "./storage";
import { checkoutSchema } from "@shared/schema";
import { generatePayPalInvoice } from "./services/paypal";
import { createPayPalInvoice } from "./services/paypal-invoice";
import { sendInvoiceEmail } from "./services/email";
import { getPaymentConfig, configStorage } from "./config-storage";
import { ZodError } from "zod";
import adminRouter from "./routes/admin";
import authRouter from "./routes/auth";
import { customCheckoutRouter } from "./routes/custom-checkout";
import { allowedEmailsRouter } from "./routes/allowed-emails";
import { adminEmailRouter } from "./routes/admin-email";
import { sendEmailRouter } from "./routes/send-email";
import { emailTemplatesRouter } from "./routes/email-templates";
import { paypalButtonsRouter } from "./routes/paypal-buttons";
import { customInvoicesRouter } from "./routes/custom-invoices";
import { generalSettingsRouter } from "./routes/general-settings";
import { homepageRouter } from "./routes/homepage";
import devicesRouter from "./routes/devices";
import recoveryRouter from "./routes/recovery";
import { systemUpdatesRouter } from "./routes/system-updates";
import { dataExportImportRouter } from "./routes/data-export-import";
import uploadRouter from "./routes/upload";
import invoicesRouter from "./routes/invoices";
import { systemMessagesRouter } from "./routes/system-messages";
import { exportAllowedEmailsRouter } from "./routes/export-allowed-emails";
import redirectRouter from "./routes/redirect";
import { contactRouter } from "./routes/contact";
import { telegramRouter } from "./routes/telegram";
import { embedCodesRouter } from "./routes/embed-codes";
import systemMonitorRouter from "./routes/system-monitor";
import { forceInitRouter } from "./routes/force-init";

// Admin authentication middleware
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session:', req.session);

  if (req.session.isAdmin) {
    console.log('Admin session verified:', true);
    next();
  } else {
    console.log('Admin session verified:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Ensure trial payment providers exist
function ensureTrialPaymentProvidersExist() {
  console.log('Checking if trial payment providers exist...');
  console.log('Current payment providers:', configStorage.payment.providers.map(p => p.id));

  // Check if trial-custom-link provider exists
  const trialCustomLinkProviderIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (trialCustomLinkProviderIndex === -1) {
    console.log('Creating trial-custom-link provider...');
    configStorage.payment.providers.push({
      id: 'trial-custom-link',
      name: 'Trial Custom Payment Links',
      active: true,
      config: {
        links: [
          {
            id: 'trial-link-1',
            name: 'Default Trial Payment Link',
            paymentLink: 'https://example.com/pay-trial',
            buttonText: 'Start Trial',
            successRedirectUrl: 'https://example.com/thank-you-trial',
            active: true
          }
        ],
        rotationMethod: 'round-robin',
        lastUsedIndex: 0
      }
    });
  }

  // Check if trial-paypal-button-embed provider exists
  const trialPaypalButtonEmbedProviderIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-paypal-button-embed');
  if (trialPaypalButtonEmbedProviderIndex === -1) {
    console.log('Creating trial-paypal-button-embed provider...');
    configStorage.payment.providers.push({
      id: 'trial-paypal-button-embed',
      name: 'Trial PayPal Button Embed',
      active: true,
      config: {
        buttons: [
          {
            id: 'trial-button-1',
            name: 'Default Trial PayPal Button',
            buttonHtml: `<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 5px; padding: 15px; background-color: #f5f8ff;">
    <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;">
      Start Your Trial - {PRODUCT_NAME}
    </div>
    <div style="font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #0070ba;">
      {AMOUNT}
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name={PRODUCT_NAME}&amount={AMOUNT_NUMBER}&currency_code=USD&return={SUCCESS_URL}&cancel_return={CANCEL_URL}"
         style="display: inline-block; background-color: #0070ba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px;">
        Start Trial Now
      </a>
    </div>
    <div style="font-size: 12px; color: #666; margin-top: 15px;">
      Secure payment powered by PayPal
    </div>
  </div>
</div>`,
            description: 'Default trial PayPal button for trial subscriptions',
            active: true
          }
        ],
        rotationMethod: 'round-robin',
        lastUsedIndex: 0
      }
    });
  }

  console.log('Trial payment providers check completed');
}

// Initialize session store
const MemoryStore = createMemoryStore(session);

export async function registerRoutes(app: Express): Promise<Server> {
  // Setup session middleware
  app.use(session({
    store: new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    }),
    secret: process.env.SESSION_SECRET || 'paypal-invoice-secret',
    resave: false,
    saveUninitialized: false, // Don't create session until something stored
    cookie: {
      secure: process.env.NODE_ENV === 'production' && process.env.SECURE_COOKIES !== 'false', // Use secure cookies in production with HTTPS, unless explicitly disabled
      sameSite: 'lax',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
    proxy: process.env.NODE_ENV === 'production' // Trust the reverse proxy when in production
  }));

  // Debug middleware to log session data
  app.use((req, res, next) => {
    if (req.path.startsWith('/api/admin')) {
      console.log(`Session debug [${req.path}]:`, {
        id: req.session.id,
        isAdmin: req.session.isAdmin,
        cookie: req.session.cookie
      });
    }
    next();
  });

  // API routes
  const apiRouter = express.Router();

  // Get all products
  apiRouter.get("/products", async (req: Request, res: Response) => {
    try {
      // Get all products
      let products = await storage.getProducts();

      // Only return active products
      products = products.filter(p => p.active);

      res.json(products);
    } catch (error) {
      console.error("Error fetching products:", error);
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });

  // Get product by ID
  apiRouter.get("/products/:id", async (req: Request, res: Response) => {
    try {
      const productId = parseInt(req.params.id);
      if (isNaN(productId)) {
        return res.status(400).json({ message: "Invalid product ID" });
      }

      const product = await storage.getProduct(productId);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json(product);
    } catch (error) {
      console.error("Error fetching product:", error);
      res.status(500).json({ message: "Failed to fetch product" });
    }
  });

  // Create invoice
  apiRouter.post("/checkout", async (req: Request, res: Response) => {
    try {
      // Validate checkout data using Zod schema
      const checkoutData = checkoutSchema.parse(req.body);

      // Get product
      const product = await storage.getProduct(checkoutData.productId);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      // Get the active payment provider
      const paymentConfig = getPaymentConfig();
      const activeProvider = paymentConfig.providers.find(p => p.active);

      if (!activeProvider) {
        return res.status(500).json({
          message: 'No active payment provider configured'
        });
      }

      let invoiceResult;
      let paymentMethod = activeProvider.id;

      if (paymentMethod === 'paypal') {
        // Generate PayPal invoice using the Invoicing API for gift invoices
        invoiceResult = await createPayPalInvoice(checkoutData, product);
      } else if (paymentMethod === 'custom-link') {
        // Generate custom payment link
        const { createCustomPaymentLink } = await import('./services/custom-link');
        invoiceResult = await createCustomPaymentLink(checkoutData, product);
      } else if (paymentMethod === 'paypal-button-embed') {
        // Generate PayPal button embed
        const { createPayPalButtonEmbed } = await import('./services/paypal-button-embed');
        invoiceResult = await createPayPalButtonEmbed(checkoutData, product);
      } else {
        return res.status(500).json({
          message: `Payment provider ${activeProvider.id} is not supported`
        });
      }
      const {
        id: paypalInvoiceId,
        url: paypalInvoiceUrl,
        isSimulated,
        error,
        isDraft,
        status,
        noPayPalAccount
      } = invoiceResult;

      console.log('PayPal invoice result:', {
        id: paypalInvoiceId,
        url: paypalInvoiceUrl,
        isSimulated: isSimulated || false,
        isDraft: isDraft || false,
        status: status || 'N/A',
        noPayPalAccount: noPayPalAccount || false
      });

      // Determine the invoice status
      let invoiceStatus = "sent";
      let invoiceNotes;

      if (noPayPalAccount) {
        // If the customer's email is not valid, mark the invoice as "no_paypal"
        invoiceStatus = "no_paypal";
        invoiceNotes = `No PayPal invoice generated because the customer email is not valid.`;
      } else if (isSimulated) {
        invoiceStatus = "simulated";
        invoiceNotes = `Simulated invoice due to API error: ${error}`;
      } else if (isDraft) {
        invoiceStatus = "draft";
        invoiceNotes = `Invoice created in draft status. Status: ${status}`;
      }

      // Create invoice in our storage
      const invoice = await storage.createInvoice({
        customerName: checkoutData.fullName,
        customerEmail: checkoutData.email,
        productId: product.id,
        amount: product.price,
        status: invoiceStatus,
        paypalInvoiceId,
        paypalInvoiceUrl,
        createdAt: new Date().toISOString(),
        notes: invoiceNotes
      });

      // Send email notification based on payment method
      if (paymentMethod === 'paypal' && noPayPalAccount) {
        // Don't send email for PayPal if the customer doesn't have a PayPal account
        console.log(`No email notification sent for invoice ID: ${invoice.id} (customer has no PayPal account)`);
      } else {
        try {
          await sendInvoiceEmail(checkoutData, product, paypalInvoiceUrl, paymentMethod);
          console.log(`Email notification sent for invoice ID: ${invoice.id} using ${paymentMethod}`);
        } catch (emailError) {
          console.warn("Email notification could not be sent:", emailError);
          // We don't want to fail the checkout if just the email fails
        }
      }

      // Send Telegram notification
      try {
        const { telegramBot } = await import('./services/telegram-bot');
        await telegramBot.sendOrderNotification({
          orderId: invoice.id,
          customerName: checkoutData.fullName,
          customerEmail: checkoutData.email,
          amount: product.price,
          status: invoiceStatus,
          country: checkoutData.country || 'Unknown',
          appType: checkoutData.appType || 'Unknown',
          isTrialOrder: false,
          createdAt: invoice.createdAt,
          macAddress: checkoutData.macAddress
        });
        console.log(`Telegram notification sent for invoice ID: ${invoice.id}`);
      } catch (telegramError) {
        console.warn("Telegram notification could not be sent:", telegramError);
        // We don't want to fail the checkout if just the Telegram notification fails
      }

      // Return success response
      res.status(201).json({
        invoiceId: invoice.id,
        paypalInvoiceId,
        paypalInvoiceUrl,
        isSimulated: isSimulated || false,
        isDraft: isDraft || false,
        noPayPalAccount: noPayPalAccount || false,
        status: status || 'SENT',
        message: paymentMethod === 'custom-link'
          ? `Order processed successfully. A payment link has been sent to your email.`
          : noPayPalAccount
            ? `Order processed successfully. No PayPal invoice was generated for this email.`
            : isSimulated
              ? `A simulated invoice was created due to an error with the PayPal Invoicing API: ${error}`
              : isDraft
                ? `Invoice created successfully in draft status. You can view and send it from your PayPal account.`
                : 'Invoice created and sent successfully'
      });

    } catch (error) {
      console.error("Error processing checkout:", error);

      if (error instanceof ZodError) {
        return res.status(400).json({
          message: "Validation error",
          errors: error.errors
        });
      }

      res.status(500).json({
        message: "Failed to process checkout",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Register API routes with /api prefix
  app.use("/api", apiRouter);

  // Register admin routes
  app.use("/api/admin", adminRouter);

  // Register auth routes
  app.use("/api/admin", authRouter);

  // Register custom checkout routes
  app.use("/api/custom-checkout", customCheckoutRouter);

  // Register allowed emails routes
  app.use("/api/allowed-emails", allowedEmailsRouter);

  // Register admin email routes
  app.use("/api/admin/email", adminEmailRouter);

  // Register send email routes
  app.use("/api/send-email", sendEmailRouter);

  // Register email templates routes
  app.use("/api/email-templates", emailTemplatesRouter);

  // Register PayPal buttons routes
  app.use("/api/paypal-buttons", paypalButtonsRouter);

  // Register custom invoices routes
  app.use("/api/custom-invoices", customInvoicesRouter);

  // Register general settings routes
  app.use("/api/general-settings", generalSettingsRouter);

  // Register homepage routes
  app.use("/api/homepage", homepageRouter);

  // Register devices routes
  app.use(devicesRouter);

  // Register recovery routes
  app.use(recoveryRouter);

  // Register system updates routes
  app.use("/api/admin/system", systemUpdatesRouter);

  // Register data export/import routes
  app.use("/api/admin/data", dataExportImportRouter);

  // Register upload routes
  app.use("/api/upload", uploadRouter);

  // Register invoices routes
  app.use("/api/invoices", invoicesRouter);

  // Register system messages routes
  app.use("/api/system-messages", systemMessagesRouter);

  // Register export allowed emails route
  app.use("/api/export-allowed-emails", exportAllowedEmailsRouter);

  // Register redirect routes
  app.use("/api/redirect", redirectRouter);

  // Register short redirect routes (e.g., /r/keyword)
  app.use("/r", redirectRouter);

  // Register contact routes
  app.use("/api/contact", contactRouter);

  // Register Telegram bot routes
  app.use("/api/telegram", telegramRouter);

  // Register embed codes routes
  app.use("/api/embed-codes", embedCodesRouter);

  // Register system monitor routes
  app.use("/api/admin/system-monitor", systemMonitorRouter);

  // Register force initialization routes
  app.use("/api/force-init", forceInitRouter);

  // Serve uploaded files
  app.use("/uploads", express.static(process.cwd() + "/uploads"));

  // Direct route for adding trial payment links (frontend calls this)
  app.post("/api/direct-trial-link", isAdmin, (req: Request, res: Response) => {
    console.log('Direct route for adding trial payment link called with body:', req.body);

    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    // Ensure trial payment providers exist
    ensureTrialPaymentProvidersExist();

    // Find the Trial Custom Link provider
    const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
    console.log('Trial custom link provider index:', providerIndex);

    if (providerIndex === -1) {
      console.error('Trial custom payment link provider not found');
      return res.status(404).json({
        message: 'Trial custom payment link provider not found'
      });
    }

    // Get the current links
    const config = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((config as any).links) ? (config as any).links : [];

    // Generate a unique ID for the new link
    const linkId = `trial-link-${Date.now()}`;

    // Add the new link
    links.push({
      id: linkId,
      name: name || `Trial Payment Link ${links.length + 1}`,
      paymentLink: paymentLink || '',
      buttonText: buttonText || 'Start Trial',
      successRedirectUrl: successRedirectUrl || '',
      active: active !== undefined ? active : true
    });

    // Update the config
    configStorage.payment.providers[providerIndex].config = {
      ...(config as any),
      links
    };

    console.log('Added new trial custom payment link (direct route):',
      JSON.stringify(links[links.length - 1], null, 2));

    console.log('Sending response for trial custom payment link add (direct route)');
    res.json({
      message: 'Trial custom payment link added successfully',
      linkId,
      config: configStorage.payment
    });
    console.log('Response sent for trial custom payment link add (direct route)');
  });

  const httpServer = createServer(app);
  return httpServer;
}
