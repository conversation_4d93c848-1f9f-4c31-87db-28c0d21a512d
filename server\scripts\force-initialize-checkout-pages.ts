#!/usr/bin/env tsx

import { storage } from '../storage';
import fs from 'fs/promises';
import path from 'path';

async function forceInitializeCheckoutPages() {
  console.log('🚀 Force initializing checkout pages from default configuration...');
  
  try {
    // Load the default configuration directly
    const configPath = path.join(process.cwd(), 'server', 'default-configs', 'default-configuration.json');
    const configData = await fs.readFile(configPath, 'utf-8');
    const config = JSON.parse(configData);
    
    console.log(`📦 Loading configuration v${config.version} from ${config.exportDate}`);
    
    const checkoutPages = config.data.customCheckoutPages;
    
    if (!checkoutPages || checkoutPages.length === 0) {
      console.log('❌ No checkout pages found in configuration');
      return;
    }
    
    console.log(`📄 Found ${checkoutPages.length} checkout pages to initialize`);
    
    // Clear existing checkout pages in memory
    const existingPages = await storage.getCustomCheckoutPages();
    console.log(`🗑️ Clearing ${existingPages.length} existing checkout pages from memory`);
    
    for (const page of existingPages) {
      await storage.deleteCustomCheckoutPage(page.id);
    }
    
    // Add all checkout pages from configuration
    let successCount = 0;
    for (const page of checkoutPages) {
      try {
        const { id, ...pageData } = page;
        const createdPage = await storage.createCustomCheckoutPage({
          ...pageData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        
        console.log(`✅ Created checkout page: ${createdPage.title} (ID: ${createdPage.id})`);
        successCount++;
      } catch (error) {
        console.error(`❌ Error creating checkout page ${page.title}:`, error);
      }
    }
    
    console.log(`\n🎉 Successfully initialized ${successCount}/${checkoutPages.length} checkout pages!`);
    
    // Verify the pages were created
    const finalPages = await storage.getCustomCheckoutPages();
    console.log(`\n📊 Verification: ${finalPages.length} checkout pages now in memory:`);
    
    finalPages.forEach((page, index) => {
      console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
      console.log(`      Product: ${page.productName}`);
      console.log(`      Price: $${page.price}`);
      console.log(`      Payment Method: ${page.paymentMethod}`);
      console.log(`      Trial: ${page.isTrialCheckout ? 'Yes' : 'No'}`);
      console.log('');
    });
    
    console.log('✅ Force initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during force initialization:', error);
  }
}

// Run the force initialization
forceInitializeCheckoutPages().catch(console.error);
