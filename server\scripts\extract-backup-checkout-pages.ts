#!/usr/bin/env tsx

import fs from 'fs/promises';
import path from 'path';
import { createReadStream } from 'fs';
import { pipeline } from 'stream/promises';
import { createGunzip } from 'zlib';
import { Extract } from 'unzipper';

async function extractCheckoutPagesFromBackups() {
  console.log('🔍 Extracting checkout pages from backup files...');
  
  try {
    const backupsDir = path.join(process.cwd(), 'server', 'backups');
    const tempDir = path.join(process.cwd(), 'temp-backup-extract');
    
    // Create temp directory
    try {
      await fs.mkdir(tempDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
    
    // Get all backup files
    const backupFiles = await fs.readdir(backupsDir);
    const zipFiles = backupFiles.filter(file => file.endsWith('.zip'));
    
    console.log(`📦 Found ${zipFiles.length} backup files`);
    
    for (const zipFile of zipFiles) {
      console.log(`\n🔍 Examining backup: ${zipFile}`);
      
      try {
        const zipPath = path.join(backupsDir, zipFile);
        const extractPath = path.join(tempDir, zipFile.replace('.zip', ''));
        
        // Create extraction directory
        await fs.mkdir(extractPath, { recursive: true });
        
        // Extract the zip file
        await pipeline(
          createReadStream(zipPath),
          Extract({ path: extractPath })
        );
        
        console.log(`✅ Extracted ${zipFile}`);
        
        // Look for checkout page data in extracted files
        const extractedFiles = await fs.readdir(extractPath, { recursive: true });
        
        for (const file of extractedFiles) {
          const filePath = path.join(extractPath, file as string);
          
          try {
            const stats = await fs.stat(filePath);
            if (stats.isFile() && (file as string).endsWith('.json')) {
              console.log(`📄 Checking JSON file: ${file}`);
              
              const content = await fs.readFile(filePath, 'utf-8');
              const data = JSON.parse(content);
              
              // Check if this contains checkout page data
              if (data.customCheckoutPages && Array.isArray(data.customCheckoutPages) && data.customCheckoutPages.length > 0) {
                console.log(`🎉 FOUND ${data.customCheckoutPages.length} CHECKOUT PAGES in ${zipFile}!`);
                
                data.customCheckoutPages.forEach((page: any, index: number) => {
                  console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
                  console.log(`      Product: ${page.productName}`);
                  console.log(`      Price: $${page.price}`);
                  console.log(`      Payment Method: ${page.paymentMethod}`);
                  console.log('');
                });
                
                // Save the checkout pages to a separate file
                const checkoutPagesExport = {
                  version: '2.0',
                  exportDate: new Date().toISOString(),
                  source: `backup-${zipFile}`,
                  customCheckoutPages: data.customCheckoutPages
                };
                
                const configDir = path.join(process.cwd(), 'server', 'default-configs');
                await fs.mkdir(configDir, { recursive: true });
                
                const exportPath = path.join(configDir, `backup-checkout-pages-${zipFile.replace('.zip', '')}.json`);
                await fs.writeFile(exportPath, JSON.stringify(checkoutPagesExport, null, 2));
                
                console.log(`✅ Exported checkout pages to: ${exportPath}`);
                
                return data.customCheckoutPages; // Return the first set found
              }
              
              // Also check for other possible structures
              if (Array.isArray(data) && data.length > 0 && data[0].slug) {
                console.log(`🎉 FOUND ${data.length} CHECKOUT PAGES (array format) in ${zipFile}!`);
                
                data.forEach((page: any, index: number) => {
                  console.log(`   ${index + 1}. ${page.title} (${page.slug})`);
                });
                
                return data;
              }
              
              // Check for nested data structures
              for (const key in data) {
                if (data[key] && Array.isArray(data[key]) && data[key].length > 0 && data[key][0].slug) {
                  console.log(`🎉 FOUND ${data[key].length} CHECKOUT PAGES in ${key} property of ${zipFile}!`);
                  return data[key];
                }
              }
            }
          } catch (error) {
            // Skip files that can't be read or parsed
          }
        }
        
      } catch (error) {
        console.log(`❌ Error extracting ${zipFile}: ${error.message}`);
      }
    }
    
    // Clean up temp directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
    
    console.log('\n❌ No checkout pages found in any backup files');
    
  } catch (error) {
    console.error('❌ Error extracting backups:', error);
  }
}

// Run the extraction
extractCheckoutPagesFromBackups().catch(console.error);
