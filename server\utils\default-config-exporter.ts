import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as schema from '../../shared/schema';
import { 
  customCheckoutPages, 
  allowedEmails, 
  smtpProviders, 
  emailTemplates 
} from '../../shared/schema';
import { generalConfigStorage } from '../general-config';
import { configStorage } from '../config-storage';
import fs from 'fs/promises';
import path from 'path';

export interface DefaultConfigExport {
  version: string;
  exportDate: string;
  metadata: {
    description: string;
    source: string;
  };
  data: {
    customCheckoutPages: any[];
    allowedEmails: any[];
    smtpProviders: any[];
    emailTemplates: any[];
    generalSettings: any;
    paymentConfig: any;
    uploadedImages: string[];
  };
}

export async function exportCurrentConfigAsDefault(): Promise<DefaultConfigExport> {
  console.log('🔄 Starting export of current configuration as default...');

  // Use the same database file as the running app
  const sqlite = new Database('data.db');
  const db = drizzle(sqlite, { schema });

  try {
    // Export custom checkout pages
    console.log('📄 Exporting custom checkout pages...');
    let checkoutPages = [];
    try {
      checkoutPages = await db.select().from(customCheckoutPages);
      console.log(`✅ Found ${checkoutPages.length} custom checkout pages`);
    } catch (error) {
      console.log('⚠️ Custom checkout pages table not found, skipping...');
    }

    // Export allowed emails
    console.log('📧 Exporting allowed emails...');
    let emails = [];
    try {
      emails = await db.select().from(allowedEmails);
      console.log(`✅ Found ${emails.length} allowed emails`);
    } catch (error) {
      console.log('⚠️ Allowed emails table not found, skipping...');
    }

    // Export SMTP providers
    console.log('📮 Exporting SMTP providers...');
    let smtp = [];
    try {
      smtp = await db.select().from(smtpProviders);
      console.log(`✅ Found ${smtp.length} SMTP providers`);
    } catch (error) {
      console.log('⚠️ SMTP providers table not found, skipping...');
    }

    // Export email templates
    console.log('📝 Exporting email templates...');
    let templates = [];
    try {
      templates = await db.select().from(emailTemplates);
      console.log(`✅ Found ${templates.length} email templates`);
    } catch (error) {
      console.log('⚠️ Email templates table not found, skipping...');
    }

    // Export uploaded images
    console.log('🖼️ Scanning uploaded images...');
    const uploadedImages = await scanUploadedImages();
    console.log(`✅ Found ${uploadedImages.length} uploaded images`);

    // Create the export object
    const exportData: DefaultConfigExport = {
      version: '2.0',
      exportDate: new Date().toISOString(),
      metadata: {
        description: 'Complete default configuration export including all user customizations',
        source: 'local-development-setup'
      },
      data: {
        customCheckoutPages: checkoutPages,
        allowedEmails: emails,
        smtpProviders: smtp,
        emailTemplates: templates,
        generalSettings: generalConfigStorage,
        paymentConfig: configStorage.payment,
        uploadedImages: uploadedImages
      }
    };

    console.log('✅ Configuration export completed successfully');
    return exportData;

  } catch (error) {
    console.error('❌ Error exporting configuration:', error);
    throw error;
  } finally {
    // Close the database connection
    sqlite.close();
  }
}

async function scanUploadedImages(): Promise<string[]> {
  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const images: string[] = [];
    
    // Check if uploads directory exists
    try {
      await fs.access(uploadsDir);
    } catch {
      console.log('📁 No uploads directory found');
      return [];
    }

    // Read all files in uploads directory
    const files = await fs.readdir(uploadsDir, { withFileTypes: true });
    
    for (const file of files) {
      if (file.isFile()) {
        const ext = path.extname(file.name).toLowerCase();
        if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext)) {
          images.push(file.name);
        }
      }
    }

    return images;
  } catch (error) {
    console.error('Error scanning uploaded images:', error);
    return [];
  }
}

export async function saveDefaultConfig(exportData: DefaultConfigExport): Promise<void> {
  try {
    const configDir = path.join(process.cwd(), 'server', 'default-configs');
    
    // Create directory if it doesn't exist
    try {
      await fs.access(configDir);
    } catch {
      await fs.mkdir(configDir, { recursive: true });
    }

    // Save the configuration
    const configPath = path.join(configDir, 'default-configuration.json');
    await fs.writeFile(configPath, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ Default configuration saved to: ${configPath}`);
    
    // Also save a backup with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(configDir, `default-config-backup-${timestamp}.json`);
    await fs.writeFile(backupPath, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ Backup saved to: ${backupPath}`);
    
  } catch (error) {
    console.error('❌ Error saving default configuration:', error);
    throw error;
  }
}
