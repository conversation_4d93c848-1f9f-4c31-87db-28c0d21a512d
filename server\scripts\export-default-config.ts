#!/usr/bin/env tsx

import { exportCurrentConfigAsDefault, saveDefaultConfig } from '../utils/default-config-exporter';
import fs from 'fs/promises';
import path from 'path';

async function main() {
  console.log('🚀 Starting default configuration export process...');
  console.log('=====================================');
  
  try {
    // Export current configuration
    const exportData = await exportCurrentConfigAsDefault();
    
    // Save the configuration
    await saveDefaultConfig(exportData);
    
    // Copy uploaded images to default configs directory
    await copyUploadedImages(exportData.data.uploadedImages);
    
    // Create initialization flag
    await createInitializationFlag();
    
    console.log('=====================================');
    console.log('✅ DEFAULT CONFIGURATION EXPORT COMPLETED!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`   📄 Custom Checkout Pages: ${exportData.data.customCheckoutPages.length}`);
    console.log(`   📧 Allowed Emails: ${exportData.data.allowedEmails.length}`);
    console.log(`   📮 SMTP Providers: ${exportData.data.smtpProviders.length}`);
    console.log(`   📝 Email Templates: ${exportData.data.emailTemplates.length}`);
    console.log(`   🖼️ Uploaded Images: ${exportData.data.uploadedImages.length}`);
    console.log(`   ⚙️ General Settings: ✅ Exported`);
    console.log(`   💳 Payment Config: ✅ Exported`);
    console.log('');
    console.log('🎯 Your current setup is now the default configuration!');
    console.log('   When you deploy this app anywhere, it will automatically');
    console.log('   include all your custom checkout pages, SMTP settings,');
    console.log('   payment configurations, uploaded images, and settings.');
    console.log('');
    console.log('📁 Files created:');
    console.log('   • server/default-configs/default-configuration.json');
    console.log('   • server/default-configs/default-config-backup-[timestamp].json');
    console.log('   • server/default-configs/images/ (with your uploaded images)');
    console.log('   • server/default-configs/.initialized (flag file)');
    
  } catch (error) {
    console.error('❌ Export failed:', error);
    process.exit(1);
  }
}

async function copyUploadedImages(imageNames: string[]): Promise<void> {
  if (imageNames.length === 0) {
    console.log('🖼️ No images to copy');
    return;
  }
  
  console.log(`🖼️ Copying ${imageNames.length} uploaded images to default configs...`);
  
  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const defaultImagesDir = path.join(process.cwd(), 'server', 'default-configs', 'images');
    
    // Create default images directory
    try {
      await fs.access(defaultImagesDir);
    } catch {
      await fs.mkdir(defaultImagesDir, { recursive: true });
    }
    
    // Copy each image
    for (const imageName of imageNames) {
      try {
        const sourcePath = path.join(uploadsDir, imageName);
        const destPath = path.join(defaultImagesDir, imageName);
        
        await fs.copyFile(sourcePath, destPath);
        console.log(`✅ Copied image: ${imageName}`);
      } catch (error) {
        console.error(`❌ Failed to copy image ${imageName}:`, error);
      }
    }
    
    console.log('✅ All images copied to default configs');
    
  } catch (error) {
    console.error('❌ Error copying images:', error);
  }
}

async function createInitializationFlag(): Promise<void> {
  try {
    const flagPath = path.join(process.cwd(), 'server', 'default-configs', '.initialized');
    const flagData = {
      initialized: true,
      timestamp: new Date().toISOString(),
      description: 'This flag indicates that default configuration has been set up'
    };
    
    await fs.writeFile(flagPath, JSON.stringify(flagData, null, 2));
    console.log('✅ Initialization flag created');
    
  } catch (error) {
    console.error('❌ Error creating initialization flag:', error);
  }
}

// Run the script
main().catch(console.error);
